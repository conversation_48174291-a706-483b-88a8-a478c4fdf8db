#!/usr/bin/env python3
"""
将指定文件夹中的灰度图进行自适应池化到7x7大小并保存到另一个文件夹
"""

import os
import sys
import torch
import torch.nn.functional as F
import cv2
import numpy as np
from pathlib import Path
import argparse
from tqdm import tqdm
import glob

def load_grayscale_image(image_path):
    """
    加载灰度图像并转换为tensor
    
    Args:
        image_path: 图像文件路径
        
    Returns:
        torch.Tensor: 形状为 [1, 1, H, W] 的tensor
    """
    # 读取图像为灰度图
    image = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)
    
    if image is None:
        raise ValueError(f"Cannot read image: {image_path}")
    
    # 转换为tensor并添加batch和channel维度
    # [H, W] -> [1, 1, H, W]
    image_tensor = torch.from_numpy(image).float().unsqueeze(0).unsqueeze(0)
    
    return image_tensor

def adaptive_pool_to_7x7(image_tensor):
    """
    使用自适应平均池化将图像池化到7x7大小
    
    Args:
        image_tensor: 输入tensor，形状为 [1, 1, H, W]
        
    Returns:
        torch.Tensor: 池化后的tensor，形状为 [1, 1, 7, 7]
    """
    # 使用自适应平均池化
    pooled = F.adaptive_avg_pool2d(image_tensor, (7, 7))
    
    return pooled

def save_pooled_image(pooled_tensor, output_path, upscale_factor=100, add_grid=True,
                     grid_color=128, grid_width=1):
    """
    保存池化后的图像，可选择放大显示

    Args:
        pooled_tensor: 池化后的tensor，形状为 [1, 1, 7, 7]
        output_path: 输出文件路径
        upscale_factor: 放大倍数，每个像素用upscale_factor x upscale_factor的像素块显示
        add_grid: 是否添加网格线
        grid_color: 网格线颜色
        grid_width: 网格线宽度
    """
    # 移除batch和channel维度: [1, 1, 7, 7] -> [7, 7]
    image_array = pooled_tensor.squeeze().numpy()

    # 确保数值在0-255范围内
    image_array = np.clip(image_array, 0, 255).astype(np.uint8)

    if upscale_factor > 1:
        # 放大图像，每个像素用upscale_factor x upscale_factor的像素块显示
        upscaled_image = upscale_7x7_image(image_array, upscale_factor, add_grid,
                                         grid_color, grid_width)
        cv2.imwrite(str(output_path), upscaled_image)
    else:
        # 保存原始7x7图像
        cv2.imwrite(str(output_path), image_array)


def upscale_7x7_image(image_7x7, block_size=100, add_grid=True, grid_color=128, grid_width=1):
    """
    将7x7图像放大，每个像素用block_size x block_size的像素块展示

    Args:
        image_7x7: 7x7的numpy数组
        block_size: 每个像素对应的像素块大小
        add_grid: 是否添加网格线
        grid_color: 网格线颜色 (0-255)
        grid_width: 网格线宽度

    Returns:
        numpy.ndarray: 放大后的图像
    """
    # 创建放大图像
    output_size = 7 * block_size
    upscaled = np.zeros((output_size, output_size), dtype=np.uint8)

    # 填充每个像素块
    for i in range(7):
        for j in range(7):
            pixel_value = image_7x7[i, j]

            # 计算像素块的位置
            start_y = i * block_size
            end_y = start_y + block_size
            start_x = j * block_size
            end_x = start_x + block_size

            # 填充像素块
            upscaled[start_y:end_y, start_x:end_x] = pixel_value

    # 添加网格线
    if add_grid and grid_width > 0:
        # 垂直线
        for i in range(1, 7):
            x = i * block_size
            upscaled[:, x:x+grid_width] = grid_color

        # 水平线
        for i in range(1, 7):
            y = i * block_size
            upscaled[y:y+grid_width, :] = grid_color

    return upscaled

def get_image_files(input_dir, extensions=None):
    """
    获取指定目录下的所有图像文件
    
    Args:
        input_dir: 输入目录路径
        extensions: 支持的文件扩展名列表
        
    Returns:
        list: 图像文件路径列表
    """
    if extensions is None:
        extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif']
    
    image_files = []
    input_path = Path(input_dir)
    
    for ext in extensions:
        # 搜索小写和大写扩展名
        pattern1 = f"*{ext.lower()}"
        pattern2 = f"*{ext.upper()}"
        
        image_files.extend(input_path.glob(pattern1))
        image_files.extend(input_path.glob(pattern2))
    
    # 去重并排序
    image_files = sorted(list(set(image_files)))
    
    return image_files

def process_single_image(input_path, output_path, verbose=False, upscale_factor=100,
                        add_grid=True, save_original=False, grid_color=128, grid_width=1):
    """
    处理单张图像

    Args:
        input_path: 输入图像路径
        output_path: 输出图像路径
        verbose: 是否显示详细信息
        upscale_factor: 放大倍数 (0表示不放大，保存原始7x7)
        add_grid: 是否添加网格线
        save_original: 是否同时保存原始7x7图像
        grid_color: 网格线颜色
        grid_width: 网格线宽度

    Returns:
        bool: 处理是否成功
    """
    try:
        # 1. 加载灰度图像
        image_tensor = load_grayscale_image(input_path)

        if verbose:
            print(f"  Original size: {image_tensor.shape[2]}x{image_tensor.shape[3]}")

        # 2. 自适应池化到7x7
        pooled_tensor = adaptive_pool_to_7x7(image_tensor)

        if verbose:
            print(f"  Pooled size: {pooled_tensor.shape[2]}x{pooled_tensor.shape[3]}")

        # 3. 保存结果
        if upscale_factor > 1:
            # 保存放大图像
            save_pooled_image(pooled_tensor, output_path, upscale_factor, add_grid,
                            grid_color, grid_width)
            final_size = 7 * upscale_factor

            if verbose:
                print(f"  Upscaled to: {final_size}x{final_size}")
                print(f"  Grid: {'Yes' if add_grid else 'No'}")

            # 可选：同时保存原始7x7图像
            if save_original:
                original_path = Path(output_path)
                original_output = original_path.parent / f"{original_path.stem}_7x7{original_path.suffix}"
                save_pooled_image(pooled_tensor, original_output, upscale_factor=1, add_grid=False)
                if verbose:
                    print(f"  Original 7x7 saved to: {original_output}")
        else:
            # 保存原始7x7图像
            save_pooled_image(pooled_tensor, output_path, upscale_factor=1, add_grid=False)

        if verbose:
            print(f"  Saved to: {output_path}")

        return True

    except Exception as e:
        print(f"Error processing {input_path}: {e}")
        return False

def batch_process_images(input_dir, output_dir, extensions=None, verbose=False,
                        upscale_factor=100, add_grid=True, save_original=False,
                        grid_color=128, grid_width=1):
    """
    批量处理图像

    Args:
        input_dir: 输入目录
        output_dir: 输出目录
        extensions: 支持的文件扩展名
        verbose: 是否显示详细信息
        upscale_factor: 放大倍数
        add_grid: 是否添加网格线
        save_original: 是否同时保存原始7x7图像
        grid_color: 网格线颜色
        grid_width: 网格线宽度

    Returns:
        tuple: (成功数量, 总数量)
    """
    # 创建输出目录
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    # 获取所有图像文件
    image_files = get_image_files(input_dir, extensions)
    
    if not image_files:
        print(f"No image files found in {input_dir}")
        return 0, 0
    
    print(f"Found {len(image_files)} image files")
    print(f"Input directory: {input_dir}")
    print(f"Output directory: {output_dir}")
    if upscale_factor > 1:
        final_size = 7 * upscale_factor
        print(f"Upscale factor: {upscale_factor}x (7x7 -> {final_size}x{final_size})")
        print(f"Grid lines: {'Yes' if add_grid else 'No'}")
    else:
        print("Output: Original 7x7 size")
    print()

    # 处理每张图像
    success_count = 0

    for input_file in tqdm(image_files, desc="Processing images"):
        # 构建输出文件路径（保持相同的文件名）
        output_file = output_path / input_file.name

        if verbose:
            print(f"Processing: {input_file.name}")

        # 处理图像
        if process_single_image(input_file, output_file, verbose, upscale_factor,
                               add_grid, save_original, grid_color, grid_width):
            success_count += 1

        if verbose:
            print()

    return success_count, len(image_files)

def main():
    parser = argparse.ArgumentParser(
        description='Adaptive pool grayscale images to 7x7 size with optional upscaling',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # 基本使用 (池化到7x7并放大到700x700)
  python adaptive_pool_grayscale.py --input ./input_images --output ./output_upscaled

  # 只保存7x7原始尺寸
  python adaptive_pool_grayscale.py --input ./input --output ./output --no-upscale

  # 自定义放大倍数 (7x7 -> 350x350)
  python adaptive_pool_grayscale.py --input ./input --output ./output --upscale-factor 50

  # 不添加网格线
  python adaptive_pool_grayscale.py --input ./input --output ./output --no-grid

  # 同时保存7x7和放大版本
  python adaptive_pool_grayscale.py --input ./input --output ./output --save-original

  # 处理单张图像
  python adaptive_pool_grayscale.py --input image.png --output pooled_image.png --verbose
        """
    )
    
    parser.add_argument('--input', '-i', type=str, required=True,
                       help='Input directory or single image file path')
    parser.add_argument('--output', '-o', type=str, required=True,
                       help='Output directory or single image file path')
    parser.add_argument('--extensions', nargs='+',
                       default=['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'],
                       help='Supported file extensions (default: .jpg .jpeg .png .bmp .tiff .tif)')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Show detailed processing information')
    parser.add_argument('--upscale-factor', type=int, default=100,
                       help='Upscale factor for each pixel (default: 100, 0 for no upscaling)')
    parser.add_argument('--no-upscale', action='store_true',
                       help='Do not upscale, save original 7x7 images')
    parser.add_argument('--no-grid', action='store_true',
                       help='Do not add grid lines to upscaled images')
    parser.add_argument('--save-original', action='store_true',
                       help='Also save original 7x7 images alongside upscaled versions')
    parser.add_argument('--grid-color', type=int, default=128,
                       help='Grid line color (0-255, default: 128)')
    parser.add_argument('--grid-width', type=int, default=1,
                       help='Grid line width in pixels (default: 1)')
    
    args = parser.parse_args()

    # 检查输入路径
    input_path = Path(args.input)
    if not input_path.exists():
        print(f"Error: Input path {args.input} does not exist!")
        return 1

    # 处理参数
    upscale_factor = 0 if args.no_upscale else args.upscale_factor
    add_grid = not args.no_grid

    # 参数验证
    if args.upscale_factor < 0:
        print("Error: Upscale factor must be non-negative!")
        return 1

    if not (0 <= args.grid_color <= 255):
        print("Error: Grid color must be between 0 and 255!")
        return 1

    if args.grid_width < 0:
        print("Error: Grid width must be non-negative!")
        return 1
    
    try:
        if input_path.is_file():
            # 处理单张图像
            print("Processing single image...")
            print(f"Input: {args.input}")
            print(f"Output: {args.output}")
            if upscale_factor > 1:
                final_size = 7 * upscale_factor
                print(f"Upscale: 7x7 -> {final_size}x{final_size}")
                print(f"Grid: {'Yes' if add_grid else 'No'}")
            else:
                print("Output: Original 7x7 size")

            # 确保输出目录存在
            output_path = Path(args.output)
            output_path.parent.mkdir(parents=True, exist_ok=True)

            success = process_single_image(input_path, output_path, args.verbose,
                                         upscale_factor, add_grid, args.save_original,
                                         args.grid_color, args.grid_width)

            if success:
                print("✓ Image processed successfully!")
                return 0
            else:
                print("✗ Failed to process image!")
                return 1
                
        elif input_path.is_dir():
            # 批量处理目录
            print("Processing directory...")

            success_count, total_count = batch_process_images(
                args.input,
                args.output,
                args.extensions,
                args.verbose,
                upscale_factor,
                add_grid,
                args.save_original,
                args.grid_color,
                args.grid_width
            )

            print(f"\nProcessing complete!")
            print(f"Successfully processed: {success_count}/{total_count} images")

            if success_count > 0:
                if upscale_factor > 1:
                    final_size = 7 * upscale_factor
                    print(f"✓ Images processed and upscaled to {final_size}x{final_size}!")
                else:
                    print("✓ Images processed to 7x7 size!")

            if success_count == total_count:
                print("✓ All images processed successfully!")
                return 0
            else:
                print(f"⚠️  {total_count - success_count} images failed to process")
                return 1
        else:
            print(f"Error: {args.input} is neither a file nor a directory!")
            return 1
            
    except KeyboardInterrupt:
        print("\nProcessing interrupted by user")
        return 1
    except Exception as e:
        print(f"Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
