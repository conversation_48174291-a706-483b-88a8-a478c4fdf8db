#!/usr/bin/env python3
"""
快速测试脚本 - 验证特征可视化工具
"""

import os
import sys
import torch
import numpy as np
import cv2
from pathlib import Path

def create_test_image(size=(640, 480)):
    """创建简单的测试图像"""
    height, width = size
    image = np.zeros((height, width, 3), dtype=np.uint8)
    
    # 添加简单图案
    cv2.rectangle(image, (50, 50), (200, 150), (255, 0, 0), -1)  # 红色矩形
    cv2.circle(image, (400, 200), 80, (0, 255, 0), -1)  # 绿色圆形
    
    # 保存测试图像
    test_image_path = "quick_test_image.jpg"
    cv2.imwrite(test_image_path, image)
    print(f"Created test image: {test_image_path}")
    return test_image_path

def test_model_creation():
    """测试模型创建"""
    print("=== Testing Model Creation ===")
    
    try:
        from simple_feature_visualizer import create_simple_vit_base
        model = create_simple_vit_base()
        print("✓ Model created successfully")
        
        # 测试不同尺寸的输入
        test_sizes = [
            (1, 3, 518, 518),  # 标准尺寸
            (1, 3, 800, 600),  # 不同比例
            (1, 3, 1200, 800), # 更大尺寸
        ]
        
        model.eval()
        for size in test_sizes:
            test_input = torch.randn(size)
            with torch.no_grad():
                try:
                    features, H, W = model(test_input)
                    print(f"✓ Input {size[2]}x{size[3]} -> Features {features.shape}, Grid {H}x{W}")
                except Exception as e:
                    print(f"✗ Input {size[2]}x{size[3]} failed: {e}")
        
        return True
        
    except Exception as e:
        print(f"✗ Model creation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_preprocessing():
    """测试图像预处理"""
    print("\n=== Testing Image Preprocessing ===")
    
    # 创建测试图像
    test_image = create_test_image()
    
    try:
        from simple_feature_visualizer import preprocess_image
        
        # 测试预处理
        image_tensor, image_size = preprocess_image(test_image)
        print(f"✓ Preprocessing successful")
        print(f"  Input tensor shape: {image_tensor.shape}")
        print(f"  Image size: {image_size}")
        
        # 清理
        os.remove(test_image)
        return True
        
    except Exception as e:
        print(f"✗ Preprocessing failed: {e}")
        if os.path.exists(test_image):
            os.remove(test_image)
        return False

def test_full_pipeline():
    """测试完整流程"""
    print("\n=== Testing Full Pipeline ===")
    
    # 创建测试图像
    test_image = create_test_image()
    output_dir = "quick_test_output"
    
    try:
        # 导入必要模块
        from simple_feature_visualizer import (
            create_simple_vit_base, 
            preprocess_image, 
            visualize_features
        )
        
        # 1. 创建模型
        model = create_simple_vit_base()
        model.eval()
        print("✓ Model created")
        
        # 2. 预处理图像
        image_tensor, image_size = preprocess_image(test_image)
        print("✓ Image preprocessed")
        
        # 3. 提取特征
        with torch.no_grad():
            features, H, W = model(image_tensor)
        print(f"✓ Features extracted: {features.shape}")
        
        # 4. 可视化 (只保存前10个channel进行快速测试)
        # 修改features只包含前10个channel
        features_small = features[:, :, :10]  # 只取前10个channel
        
        # 手动创建简化的可视化
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        B, N, C = features_small.shape
        print(f"Visualizing {C} channels...")
        
        # 重新整形
        spatial_features = features_small.view(B, H, W, C).permute(0, 3, 1, 2).squeeze(0)
        
        # 保存前几个channel
        channel_dir = output_path / "test_channels"
        channel_dir.mkdir(exist_ok=True)
        
        for c in range(min(C, 5)):  # 只保存前5个
            channel_feature = spatial_features[c].cpu().numpy()
            
            # 归一化
            if channel_feature.max() > channel_feature.min():
                channel_normalized = (channel_feature - channel_feature.min()) / (channel_feature.max() - channel_feature.min())
            else:
                channel_normalized = np.zeros_like(channel_feature)
            
            channel_uint8 = (channel_normalized * 255).astype(np.uint8)
            
            # 保存
            channel_path = channel_dir / f"channel_{c:03d}.png"
            cv2.imwrite(str(channel_path), channel_uint8)
        
        print(f"✓ Saved {min(C, 5)} test channels to {channel_dir}")
        
        # 清理
        os.remove(test_image)
        
        print("✓ Full pipeline test successful!")
        return True
        
    except Exception as e:
        print(f"✗ Full pipeline failed: {e}")
        import traceback
        traceback.print_exc()
        
        # 清理
        if os.path.exists(test_image):
            os.remove(test_image)
        return False

def test_command_line():
    """测试命令行调用"""
    print("\n=== Testing Command Line Interface ===")
    
    # 创建测试图像
    test_image = create_test_image()
    output_dir = "cli_test_output"
    
    try:
        import subprocess
        
        cmd = [
            sys.executable, "simple_feature_visualizer.py",
            "--image", test_image,
            "--output", output_dir,
            "--device", "cpu"  # 使用CPU避免CUDA问题
        ]
        
        print(f"Running: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print("✓ Command line test successful!")
            print("Output preview:")
            print(result.stdout[-500:])  # 显示最后500字符
            
            # 检查输出文件
            output_path = Path(output_dir)
            if output_path.exists():
                channel_dir = output_path / "quick_test_image_channels"
                if channel_dir.exists():
                    channel_files = list(channel_dir.glob("*.png"))
                    print(f"✓ Generated {len(channel_files)} channel files")
            
            return True
        else:
            print("✗ Command line test failed!")
            print("Error output:")
            print(result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("✗ Command line test timed out")
        return False
    except Exception as e:
        print(f"✗ Command line test failed: {e}")
        return False
    finally:
        # 清理
        if os.path.exists(test_image):
            os.remove(test_image)

def main():
    print("Quick Test for Feature Visualizer")
    print("=" * 40)
    
    # 检查环境
    print(f"Python: {sys.version.split()[0]}")
    print(f"PyTorch: {torch.__version__}")
    print(f"CUDA available: {torch.cuda.is_available()}")
    print()
    
    # 运行测试
    tests = [
        ("Model Creation", test_model_creation),
        ("Image Preprocessing", test_preprocessing),
        ("Full Pipeline", test_full_pipeline),
        ("Command Line", test_command_line),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except KeyboardInterrupt:
            print(f"\n{test_name} interrupted by user")
            break
        except Exception as e:
            print(f"\n{test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 40)
    print("Test Results:")
    for test_name, result in results:
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"  {test_name}: {status}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The feature visualizer is working correctly.")
    else:
        print("⚠️  Some tests failed. Please check the error messages above.")

if __name__ == "__main__":
    main()
