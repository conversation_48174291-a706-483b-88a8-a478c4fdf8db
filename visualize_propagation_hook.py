#!/usr/bin/env python3
"""
使用Hook机制可视化DE-ViT第一个传播层特征 (不修改源码)
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import torch
import numpy as np
import cv2
import matplotlib.pyplot as plt
from pathlib import Path
import argparse
from detectron2.config import get_cfg
import detectron2.data.transforms as T
import detectron2.data.detection_utils as utils
from tools.train_net import Trainer

class PropagationFeatureExtractor:
    """专门用于提取传播层特征的类"""
    
    def __init__(self):
        self.features = {}
        self.hooks = []
        self.target_found = False
    
    def register_hooks(self, model):
        """注册hooks到第一个传播层"""
        print("Searching for propagation layers...")
        
        for name, module in model.named_modules():
            # 查找per_cls_cnn (前景传播网络)
            if 'per_cls_cnn' in name and hasattr(module, 'main_layers'):
                print(f"Found propagation network: {name}")
                
                # 获取第一个传播层
                first_layer = module.main_layers[0]
                
                # 注册hook到整个Sequential层的输出
                hook = first_layer.register_forward_hook(
                    lambda module, input, output, layer_name=name: self._save_features(
                        module, input, output, f"{layer_name}.main_layers[0]"
                    )
                )
                self.hooks.append(hook)
                self.target_found = True
                print(f"✓ Registered hook for: {name}.main_layers[0]")
                
                # 也可以注册到具体的Conv2d层
                for i, (sub_name, sub_module) in enumerate(first_layer.named_children()):
                    if isinstance(sub_module, torch.nn.Conv2d):
                        hook = sub_module.register_forward_hook(
                            lambda module, input, output, layer_name=f"{name}.main_layers[0].{sub_name}": 
                            self._save_features(module, input, output, layer_name)
                        )
                        self.hooks.append(hook)
                        print(f"✓ Registered hook for Conv2d: {name}.main_layers[0].{sub_name}")
                        break
                
                break  # 只处理第一个找到的传播网络
        
        if not self.target_found:
            print("⚠️  No propagation layers found. Trying alternative search...")
            self._register_alternative_hooks(model)
        
        return len(self.hooks) > 0
    
    def _register_alternative_hooks(self, model):
        """备选hook注册方法"""
        for name, module in model.named_modules():
            # 查找PropagateNet类
            if module.__class__.__name__ == 'PropagateNet':
                print(f"Found PropagateNet: {name}")
                
                if hasattr(module, 'main_layers') and len(module.main_layers) > 0:
                    first_layer = module.main_layers[0]
                    hook = first_layer.register_forward_hook(
                        lambda module, input, output, layer_name=name: 
                        self._save_features(module, input, output, f"{layer_name}.main_layers[0]")
                    )
                    self.hooks.append(hook)
                    self.target_found = True
                    print(f"✓ Alternative hook registered: {name}.main_layers[0]")
                    break
    
    def _save_features(self, module, input, output, layer_name):
        """Hook函数：保存特征"""
        if isinstance(output, torch.Tensor):
            self.features[layer_name] = output.detach().cpu()
            print(f"Captured features from {layer_name}: {output.shape}")
        else:
            print(f"Unexpected output type from {layer_name}: {type(output)}")
    
    def get_features(self):
        """获取捕获的特征"""
        return self.features
    
    def cleanup(self):
        """清理hooks"""
        for hook in self.hooks:
            hook.remove()
        self.hooks.clear()
        print("✓ Hooks cleaned up")

def setup_config(config_file, model_path, device='cuda'):
    """设置配置"""
    config = get_cfg()
    config.merge_from_file(config_file)

    config.MODEL.DEVICE = device
    config.MODEL.WEIGHTS = model_path
    config.MODEL.MASK_ON = True
    config.MODEL.ROI_HEADS.NUM_CLASSES = 80  # COCO classes

    # 修复OFFLINE_RPN_CONFIG问题
    if not hasattr(config.DE, 'OFFLINE_RPN_CONFIG') or config.DE.OFFLINE_RPN_CONFIG is None or config.DE.OFFLINE_RPN_CONFIG == '':
        config.DE.OFFLINE_RPN_CONFIG = "configs/RPN/mask_rcnn_R_50_FPN_1x.yaml"
        print(f"Set OFFLINE_RPN_CONFIG to: {config.DE.OFFLINE_RPN_CONFIG}")

    config.freeze()
    return config

def load_model(config, model_path, device='cuda'):
    """加载模型"""
    print(f"Loading model from: {model_path}")
    
    model = Trainer.build_model(config).to(device)
    checkpoint = torch.load(model_path, map_location=device)
    model.load_state_dict(checkpoint['model'])
    model.eval()
    
    print("✓ Model loaded successfully!")
    return model

def preprocess_image(image_path, config):
    """预处理图像"""
    print(f"Processing image: {image_path}")
    
    image = utils.read_image(image_path, format="RGB")
    height, width = image.shape[0], image.shape[1]
    print(f"Original image size: {width}x{height}")
    
    augs = utils.build_augmentation(config, False)
    augmentations = T.AugmentationList(augs)
    
    aug_input = T.AugInput(image)
    augmentations(aug_input)
    
    dataset_dict = {
        "height": height,
        "width": width,
        "image": torch.as_tensor(np.ascontiguousarray(aug_input.image.transpose(2, 0, 1)))
    }
    
    return [dataset_dict], image

def visualize_features(features_dict, output_dir, max_channels=16):
    """可视化所有捕获的特征"""
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    for layer_name, features in features_dict.items():
        print(f"Visualizing {layer_name}: {features.shape}")
        
        if len(features.shape) != 4:
            print(f"Skipping {layer_name}: unexpected shape {features.shape}")
            continue
        
        batch_size, num_channels, height, width = features.shape
        features = features[0]  # 取第一个batch
        num_channels = min(num_channels, max_channels)
        
        # 创建网格可视化
        cols = 4
        rows = (num_channels + cols - 1) // cols
        
        fig, axes = plt.subplots(rows, cols, figsize=(cols*3, rows*3))
        if rows == 1:
            axes = axes.reshape(1, -1)
        elif cols == 1:
            axes = axes.reshape(-1, 1)
        
        for i in range(num_channels):
            row = i // cols
            col = i % cols
            
            feature_map = features[i].numpy()
            
            # 归一化到0-1
            if feature_map.max() > feature_map.min():
                feature_map = (feature_map - feature_map.min()) / (feature_map.max() - feature_map.min())
            
            im = axes[row, col].imshow(feature_map, cmap='viridis')
            axes[row, col].set_title(f'Channel {i}', fontsize=10)
            axes[row, col].axis('off')
            
            # 添加colorbar
            plt.colorbar(im, ax=axes[row, col], fraction=0.046, pad=0.04)
        
        # 隐藏多余的子图
        for i in range(num_channels, rows * cols):
            row = i // cols
            col = i % cols
            axes[row, col].axis('off')
        
        plt.suptitle(f'Features from {layer_name}', fontsize=14)
        plt.tight_layout()
        
        # 保存大图
        safe_name = layer_name.replace('.', '_').replace('/', '_')
        output_file = output_path / f"{safe_name}_features.png"
        plt.savefig(output_file, dpi=150, bbox_inches='tight')
        plt.close()
        
        print(f"✓ Saved: {output_file}")
        
        # 保存单独的通道
        channel_dir = output_path / f"{safe_name}_channels"
        channel_dir.mkdir(exist_ok=True)
        
        for i in range(num_channels):
            feature_map = features[i].numpy()
            
            # 归一化到0-255
            if feature_map.max() > feature_map.min():
                feature_map = (feature_map - feature_map.min()) / (feature_map.max() - feature_map.min())
            feature_map = (feature_map * 255).astype(np.uint8)
            
            channel_file = channel_dir / f"channel_{i:03d}.png"
            cv2.imwrite(str(channel_file), feature_map)
        
        print(f"✓ Saved individual channels: {channel_dir}")

def main():
    parser = argparse.ArgumentParser(description='Visualize DE-ViT propagation layer features using hooks')
    parser.add_argument('--image', type=str, required=True, help='Path to input image')
    parser.add_argument('--config', type=str, 
                       default='configs/few-shot/vitb_shot10.yaml',
                       help='Path to config file')
    parser.add_argument('--model', type=str,
                       help='Path to model weights (auto-detect if not specified)')
    parser.add_argument('--output', type=str, default='propagation_features',
                       help='Output directory for visualizations')
    parser.add_argument('--device', type=str, default='cuda',
                       choices=['cuda', 'cpu'], help='Device to use')
    parser.add_argument('--max-channels', type=int, default=16,
                       help='Maximum number of channels to visualize')
    
    args = parser.parse_args()
    
    # 检查输入文件
    if not os.path.exists(args.image):
        print(f"Error: Image file {args.image} not found!")
        return 1
    
    # 自动检测模型权重
    if args.model is None:
        import glob
        patterns = [
            "weights/trained/few-shot/coco/vitb_shot10_*.pth",
            "weights/trained/few-shot/vitb_shot10_*.pth",
            "weights/**/vitb*shot10*.pth",
            "weights/**/*shot10*.pth"
        ]
        
        model_files = []
        for pattern in patterns:
            model_files.extend(glob.glob(pattern, recursive=True))
        
        if not model_files:
            print("Error: No model weights found. Please specify --model")
            print("Expected patterns:")
            for pattern in patterns:
                print(f"  {pattern}")
            return 1
        
        args.model = model_files[0]
        print(f"Auto-detected model: {args.model}")
    
    # 检查设备
    if args.device == 'cuda' and not torch.cuda.is_available():
        print("CUDA not available, using CPU")
        args.device = 'cpu'
    
    # 创建特征提取器
    extractor = PropagationFeatureExtractor()
    
    try:
        # 1. 设置配置
        config = setup_config(args.config, args.model, args.device)
        
        # 2. 加载模型
        model = load_model(config, args.model, args.device)
        
        # 3. 注册hooks
        if not extractor.register_hooks(model):
            print("Error: Failed to register hooks to propagation layers")
            return 1
        
        # 4. 预处理图像
        batched_inputs, original_image = preprocess_image(args.image, config)
        
        # 5. 运行推理
        print("Running inference with feature capture...")
        for item in batched_inputs:
            item["image"] = item["image"].to(args.device)
        
        with torch.no_grad():
            outputs = model(batched_inputs)
        
        # 6. 获取特征
        captured_features = extractor.get_features()
        
        if captured_features:
            print(f"\n✓ Captured features from {len(captured_features)} layer(s)")
            for layer_name, features in captured_features.items():
                print(f"  {layer_name}: {features.shape}")
            
            # 7. 可视化
            visualize_features(captured_features, args.output, args.max_channels)
            
            print(f"\n🎉 Feature visualization completed!")
            print(f"Output directory: {args.output}")
        else:
            print("\n❌ No features were captured!")
            print("This might happen if:")
            print("1. The model architecture is different than expected")
            print("2. The propagation layers are not being used during inference")
            print("3. The hook registration failed")
        
        return 0
        
    except Exception as e:
        print(f"Error during processing: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    finally:
        # 清理hooks
        extractor.cleanup()

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
