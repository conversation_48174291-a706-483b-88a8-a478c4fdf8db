#!/usr/bin/env python3
"""
测试特征可视化工具
"""

import os
import sys
import torch
import numpy as np
import cv2
from pathlib import Path

def create_test_image(output_path="test_image.jpg", size=(640, 480)):
    """创建测试图像"""
    # 创建一个彩色测试图像
    height, width = size
    image = np.zeros((height, width, 3), dtype=np.uint8)
    
    # 添加一些图案
    # 红色矩形
    cv2.rectangle(image, (50, 50), (200, 150), (255, 0, 0), -1)
    
    # 绿色圆形
    cv2.circle(image, (400, 200), 80, (0, 255, 0), -1)
    
    # 蓝色三角形
    pts = np.array([[300, 350], [450, 350], [375, 250]], np.int32)
    cv2.fillPoly(image, [pts], (0, 0, 255))
    
    # 添加一些文字
    cv2.putText(image, "Test Image", (200, 400), cv2.FONT_HERSHEY_SIMPLEX, 2, (255, 255, 255), 3)
    
    # 保存图像
    cv2.imwrite(output_path, image)
    print(f"Test image created: {output_path}")
    return output_path


def test_simple_visualizer():
    """测试简化版可视化工具"""
    print("=== Testing Simple Feature Visualizer ===")
    
    # 创建测试图像
    test_image = create_test_image()
    
    # 测试参数
    output_dir = "test_output_simple"
    
    # 构建命令
    cmd = [
        sys.executable, "simple_feature_visualizer.py",
        "--image", test_image,
        "--output", output_dir,
        "--device", "cuda" if torch.cuda.is_available() else "cpu"
    ]
    
    print(f"Running command: {' '.join(cmd)}")
    
    try:
        import subprocess
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✓ Simple visualizer test passed!")
        print("Output:")
        print(result.stdout)
        
        # 检查输出文件
        output_path = Path(output_dir)
        if output_path.exists():
            channel_dir = output_path / "test_image_channels"
            if channel_dir.exists():
                channel_files = list(channel_dir.glob("channel_*.png"))
                print(f"✓ Generated {len(channel_files)} channel images")
            
            overview_file = output_path / "test_image_overview.png"
            if overview_file.exists():
                print("✓ Generated overview image")
        
    except subprocess.CalledProcessError as e:
        print("✗ Simple visualizer test failed!")
        print("Error output:")
        print(e.stderr)
        print("Standard output:")
        print(e.stdout)
    except Exception as e:
        print(f"✗ Test failed with exception: {e}")


def test_original_visualizer():
    """测试原始可视化工具"""
    print("\n=== Testing Original Feature Visualizer ===")
    
    # 创建测试图像
    test_image = create_test_image("test_image2.jpg")
    
    # 测试参数
    output_dir = "test_output_original"
    
    # 构建命令
    cmd = [
        sys.executable, "visualize_backbone_features.py",
        "--image", test_image,
        "--output", output_dir,
        "--device", "cuda" if torch.cuda.is_available() else "cpu"
    ]
    
    print(f"Running command: {' '.join(cmd)}")
    
    try:
        import subprocess
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✓ Original visualizer test passed!")
        print("Output:")
        print(result.stdout)
        
        # 检查输出文件
        output_path = Path(output_dir)
        if output_path.exists():
            channel_dir = output_path / "test_image2_channels"
            if channel_dir.exists():
                channel_files = list(channel_dir.glob("channel_*.png"))
                print(f"✓ Generated {len(channel_files)} channel images")
            
            overview_file = output_path / "test_image2_overview.png"
            if overview_file.exists():
                print("✓ Generated overview image")
        
    except subprocess.CalledProcessError as e:
        print("✗ Original visualizer test failed!")
        print("Error output:")
        print(e.stderr)
        print("Standard output:")
        print(e.stdout)
    except Exception as e:
        print(f"✗ Test failed with exception: {e}")


def test_import_issues():
    """测试导入问题"""
    print("\n=== Testing Import Issues ===")
    
    try:
        # 测试简化版导入
        from simple_feature_visualizer import create_simple_vit_base
        model = create_simple_vit_base()
        print("✓ Simple visualizer imports work")
        
        # 测试模型创建
        test_input = torch.randn(1, 3, 518, 518)
        with torch.no_grad():
            features, H, W = model(test_input)
        print(f"✓ Model forward pass works: {features.shape}")
        
    except Exception as e:
        print(f"✗ Import test failed: {e}")
        import traceback
        traceback.print_exc()
    
    try:
        # 测试原始版导入
        from visualize_backbone_features import create_vit_base_model
        model = create_vit_base_model('cpu')
        print("✓ Original visualizer imports work")
        
    except Exception as e:
        print(f"✗ Original visualizer import failed: {e}")


def cleanup_test_files():
    """清理测试文件"""
    print("\n=== Cleaning up test files ===")
    
    # 删除测试图像
    test_files = ["test_image.jpg", "test_image2.jpg"]
    for file in test_files:
        if os.path.exists(file):
            os.remove(file)
            print(f"Removed {file}")
    
    # 删除输出目录
    import shutil
    output_dirs = ["test_output_simple", "test_output_original"]
    for dir_path in output_dirs:
        if os.path.exists(dir_path):
            shutil.rmtree(dir_path)
            print(f"Removed {dir_path}")


def main():
    print("Feature Visualizer Test Suite")
    print("=" * 50)
    
    # 检查环境
    print(f"Python: {sys.version}")
    print(f"PyTorch: {torch.__version__}")
    print(f"CUDA available: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"CUDA device: {torch.cuda.get_device_name()}")
    print()
    
    try:
        # 测试导入
        test_import_issues()
        
        # 测试简化版
        test_simple_visualizer()
        
        # 测试原始版
        test_original_visualizer()
        
    except KeyboardInterrupt:
        print("\nTest interrupted by user")
    except Exception as e:
        print(f"\nUnexpected error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 清理
        cleanup_test_files()
    
    print("\n=== Test Complete ===")


if __name__ == "__main__":
    main()
