#!/usr/bin/env python3
"""
测试修改后的自适应池化脚本的放大功能
"""

import os
import cv2
import numpy as np
from pathlib import Path
import subprocess
import sys

def create_test_image(size=(256, 192), filename="test_input.png"):
    """创建测试图像"""
    height, width = size
    image = np.zeros((height, width), dtype=np.uint8)
    
    # 创建一些图案
    # 渐变背景
    for y in range(height):
        for x in range(width):
            image[y, x] = int((x + y) * 255 / (width + height))
    
    # 添加矩形
    cv2.rectangle(image, (width//4, height//4), (3*width//4, 3*height//4), 128, -1)
    
    # 添加圆形
    center = (width//2, height//2)
    radius = min(width, height) // 6
    cv2.circle(image, center, radius, 255, -1)
    
    # 保存图像
    cv2.imwrite(filename, image)
    print(f"Created test image: {filename} ({width}x{height})")
    return filename

def test_basic_upscaling():
    """测试基本放大功能"""
    print("=== 测试基本放大功能 ===")
    
    # 创建测试图像
    test_image = create_test_image()
    output_image = "test_upscaled_700x700.png"
    
    try:
        # 运行脚本 (默认100x放大)
        cmd = [
            "python", "adaptive_pool_grayscale.py",
            "--input", test_image,
            "--output", output_image,
            "--verbose"
        ]
        
        print(f"运行命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ 基本放大测试通过!")
            
            # 检查输出图像尺寸
            if os.path.exists(output_image):
                img = cv2.imread(output_image, cv2.IMREAD_GRAYSCALE)
                if img is not None:
                    print(f"✓ 输出图像尺寸: {img.shape} (期望: (700, 700))")
                    if img.shape == (700, 700):
                        print("✓ 尺寸正确!")
                    else:
                        print("✗ 尺寸不正确!")
                
                # 清理
                os.remove(output_image)
            else:
                print("✗ 输出图像未找到")
        else:
            print("✗ 基本放大测试失败!")
            print("错误:", result.stderr)
            
    except Exception as e:
        print(f"✗ 测试失败: {e}")
    finally:
        if os.path.exists(test_image):
            os.remove(test_image)

def test_custom_upscale_factor():
    """测试自定义放大倍数"""
    print("\n=== 测试自定义放大倍数 ===")
    
    test_image = create_test_image(filename="test_custom.png")
    output_image = "test_custom_350x350.png"
    
    try:
        # 测试50x放大 (7x7 -> 350x350)
        cmd = [
            "python", "adaptive_pool_grayscale.py",
            "--input", test_image,
            "--output", output_image,
            "--upscale-factor", "50",
            "--verbose"
        ]
        
        print(f"运行命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ 自定义放大测试通过!")
            
            # 检查输出图像尺寸
            if os.path.exists(output_image):
                img = cv2.imread(output_image, cv2.IMREAD_GRAYSCALE)
                if img is not None:
                    print(f"✓ 输出图像尺寸: {img.shape} (期望: (350, 350))")
                    if img.shape == (350, 350):
                        print("✓ 自定义尺寸正确!")
                    else:
                        print("✗ 自定义尺寸不正确!")
                
                os.remove(output_image)
        else:
            print("✗ 自定义放大测试失败!")
            print("错误:", result.stderr)
            
    except Exception as e:
        print(f"✗ 测试失败: {e}")
    finally:
        if os.path.exists(test_image):
            os.remove(test_image)

def test_no_upscale():
    """测试不放大选项"""
    print("\n=== 测试不放大选项 ===")
    
    test_image = create_test_image(filename="test_no_upscale.png")
    output_image = "test_7x7_only.png"
    
    try:
        # 测试不放大
        cmd = [
            "python", "adaptive_pool_grayscale.py",
            "--input", test_image,
            "--output", output_image,
            "--no-upscale",
            "--verbose"
        ]
        
        print(f"运行命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ 不放大测试通过!")
            
            # 检查输出图像尺寸
            if os.path.exists(output_image):
                img = cv2.imread(output_image, cv2.IMREAD_GRAYSCALE)
                if img is not None:
                    print(f"✓ 输出图像尺寸: {img.shape} (期望: (7, 7))")
                    if img.shape == (7, 7):
                        print("✓ 7x7尺寸正确!")
                    else:
                        print("✗ 7x7尺寸不正确!")
                
                os.remove(output_image)
        else:
            print("✗ 不放大测试失败!")
            print("错误:", result.stderr)
            
    except Exception as e:
        print(f"✗ 测试失败: {e}")
    finally:
        if os.path.exists(test_image):
            os.remove(test_image)

def test_no_grid():
    """测试无网格选项"""
    print("\n=== 测试无网格选项 ===")
    
    test_image = create_test_image(filename="test_no_grid.png")
    output_image = "test_no_grid_700x700.png"
    
    try:
        # 测试无网格
        cmd = [
            "python", "adaptive_pool_grayscale.py",
            "--input", test_image,
            "--output", output_image,
            "--no-grid",
            "--verbose"
        ]
        
        print(f"运行命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ 无网格测试通过!")
            
            # 检查输出图像
            if os.path.exists(output_image):
                img = cv2.imread(output_image, cv2.IMREAD_GRAYSCALE)
                if img is not None and img.shape == (700, 700):
                    print("✓ 无网格图像生成成功!")
                
                os.remove(output_image)
        else:
            print("✗ 无网格测试失败!")
            print("错误:", result.stderr)
            
    except Exception as e:
        print(f"✗ 测试失败: {e}")
    finally:
        if os.path.exists(test_image):
            os.remove(test_image)

def test_batch_processing():
    """测试批量处理"""
    print("\n=== 测试批量处理 ===")
    
    # 创建测试目录和图像
    input_dir = Path("test_batch_input")
    output_dir = Path("test_batch_output")
    
    input_dir.mkdir(exist_ok=True)
    
    # 创建多个测试图像
    test_files = []
    for i in range(3):
        filename = input_dir / f"test_image_{i+1}.png"
        create_test_image((128 + i*32, 96 + i*24), str(filename))
        test_files.append(filename)
    
    try:
        # 批量处理
        cmd = [
            "python", "adaptive_pool_grayscale.py",
            "--input", str(input_dir),
            "--output", str(output_dir),
            "--upscale-factor", "20",  # 小一点的放大倍数用于测试
            "--verbose"
        ]
        
        print(f"运行命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ 批量处理测试通过!")
            
            # 检查输出文件
            if output_dir.exists():
                output_files = list(output_dir.glob("*.png"))
                print(f"✓ 生成了 {len(output_files)} 个输出文件")
                
                # 检查第一个文件的尺寸
                if output_files:
                    img = cv2.imread(str(output_files[0]), cv2.IMREAD_GRAYSCALE)
                    if img is not None:
                        expected_size = 7 * 20  # 140x140
                        print(f"✓ 输出图像尺寸: {img.shape} (期望: ({expected_size}, {expected_size}))")
        else:
            print("✗ 批量处理测试失败!")
            print("错误:", result.stderr)
            
    except Exception as e:
        print(f"✗ 测试失败: {e}")
    finally:
        # 清理
        import shutil
        if input_dir.exists():
            shutil.rmtree(input_dir)
        if output_dir.exists():
            shutil.rmtree(output_dir)

def main():
    print("自适应池化脚本放大功能测试")
    print("=" * 50)
    
    # 检查依赖
    try:
        import torch
        import cv2
        import numpy as np
        from tqdm import tqdm
        print("✓ 所有依赖可用")
    except ImportError as e:
        print(f"✗ 缺少依赖: {e}")
        return
    
    print()
    
    # 运行测试
    test_basic_upscaling()
    test_custom_upscale_factor()
    test_no_upscale()
    test_no_grid()
    test_batch_processing()
    
    print("\n" + "=" * 50)
    print("测试完成!")
    print("\n新功能使用示例:")
    print("1. 默认放大到700x700:")
    print("   python adaptive_pool_grayscale.py --input image.png --output upscaled.png")
    print("\n2. 自定义放大倍数:")
    print("   python adaptive_pool_grayscale.py --input image.png --output upscaled.png --upscale-factor 50")
    print("\n3. 不放大，只保存7x7:")
    print("   python adaptive_pool_grayscale.py --input image.png --output pooled.png --no-upscale")
    print("\n4. 无网格线:")
    print("   python adaptive_pool_grayscale.py --input image.png --output upscaled.png --no-grid")

if __name__ == "__main__":
    main()
