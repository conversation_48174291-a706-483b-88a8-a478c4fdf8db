# DINOv2 ViT-Base 特征可视化工具

这个工具可以将图像输入到DE-ViT项目使用的DINOv2 ViT-Base backbone中，并将得到的特征图的每个channel可视化为灰度图像。

## 文件说明

- `visualize_backbone_features.py`: 主要的特征可视化脚本 (支持多种导入方式)
- `simple_feature_visualizer.py`: 简化版脚本 (避免循环导入问题)
- `test_feature_visualizer.py`: 测试脚本
- `example_usage.py`: 使用示例和批处理脚本生成器
- `FEATURE_VISUALIZATION_README.md`: 本说明文件

## ⚠️ 导入问题解决方案

如果遇到循环导入错误，请使用简化版脚本：

```bash
# 推荐：使用简化版 (避免导入问题)
python simple_feature_visualizer.py --image your_image.jpg --output output_dir

# 或者：使用原始版 (如果环境配置正确)
python visualize_backbone_features.py --image your_image.jpg --output output_dir
```

## 环境要求

确保您已经安装了DE-ViT项目的依赖：

```bash
pip install torch torchvision
pip install opencv-python pillow matplotlib numpy
pip install detectron2  # 按照DE-ViT项目的安装说明
```

## 使用方法

### 1. 快速测试

```bash
# 运行测试脚本 (自动创建测试图像并验证功能)
python test_feature_visualizer.py
```

### 2. 基本使用

```bash
# 推荐：使用简化版
python simple_feature_visualizer.py \
    --image path/to/your/image.jpg \
    --output output_directory \
    --device cuda

# 或者：使用完整版
python visualize_backbone_features.py \
    --image path/to/your/image.jpg \
    --output output_directory \
    --device cuda
```

### 2. 参数说明

- `--image`: 输入图像路径 (必需)
- `--output`: 输出目录路径 (必需)
- `--device`: 使用的设备 (`cuda` 或 `cpu`, 默认: `cuda`)
- `--target_size`: 短边目标尺寸 (默认: 800)
- `--max_size`: 长边最大尺寸 (默认: 1333)

### 3. 输出结果

脚本会在指定的输出目录中创建：

```
output_directory/
├── image_name_channels/          # 包含所有channel的灰度图
│   ├── channel_000.png          # 第0个channel
│   ├── channel_001.png          # 第1个channel
│   ├── ...
│   └── channel_767.png          # 第767个channel (ViT-Base有768个channel)
└── image_name_overview.png       # 前64个channel的总览图
```

## 详细说明

### 特征提取过程

1. **图像预处理**: 
   - 按照DE-ViT项目的预处理流程调整图像尺寸
   - 使用DINOv2的标准化参数进行归一化

2. **特征提取**:
   - 使用DINOv2 ViT-Base backbone (patch_size=14, embed_dim=768)
   - 提取最后一层 (第11层) 的patch token特征

3. **特征重整形**:
   - 将patch特征重新整形为空间特征图
   - 从 `[B, N, C]` 转换为 `[B, C, H, W]` 格式

4. **可视化**:
   - 每个channel单独保存为灰度图像
   - 特征值归一化到0-255范围

### 特征图尺寸

对于不同的输入图像尺寸，特征图的空间尺寸会相应变化：

| 输入尺寸 | 预处理后尺寸 | 特征图尺寸 | Patch数量 |
|---------|-------------|-----------|----------|
| 640×427 | 1200×800 | 86×57 | 4,902 |
| 800×600 | 1067×800 | 76×57 | 4,332 |
| 1024×768 | 1067×800 | 76×57 | 4,332 |

所有情况下都有768个channel (ViT-Base的特征维度)。

## 使用示例

### 示例1: 处理单张图像

```python
# 修改 example_usage.py 中的 image_path
image_path = "demo/sample_image.jpg"

# 运行
python example_usage.py
```

### 示例2: 命令行直接使用

```bash
# 处理JPEG图像
python visualize_backbone_features.py \
    --image demo/cat.jpg \
    --output visualizations/cat \
    --device cuda

# 处理PNG图像，使用CPU
python visualize_backbone_features.py \
    --image data/test.png \
    --output results/test \
    --device cpu
```

### 示例3: 批量处理

```python
# 在 example_usage.py 中取消注释
create_batch_script()

# 修改生成的 batch_visualize.py 中的图像目录
# 然后运行
python batch_visualize.py
```

## 注意事项

1. **内存使用**: ViT-Base模型较大，建议使用GPU加速
2. **存储空间**: 每张图像会生成768个channel图像，确保有足够存储空间
3. **图像格式**: 支持常见格式 (JPG, PNG, BMP, TIFF)
4. **路径设置**: 确保脚本在DE-ViT项目根目录下运行

## 故障排除

### 常见错误

1. **循环导入错误**:
   ```
   ImportError: cannot import name 'DinoVisionTransformer' from partially initialized module
   ```
   **解决方案**: 使用简化版脚本
   ```bash
   python simple_feature_visualizer.py --image your_image.jpg --output output_dir
   ```

2. **CUDA内存不足**:
   ```bash
   # 使用CPU代替
   --device cpu
   ```

3. **模块导入错误**:
   ```bash
   # 确保在项目根目录运行
   cd /path/to/devit
   python simple_feature_visualizer.py ...
   ```

4. **图像读取失败**:
   - 检查图像路径是否正确
   - 确认图像文件未损坏
   - 支持的格式: JPG, PNG, BMP, TIFF

5. **测试环境**:
   ```bash
   # 运行测试脚本验证环境
   python test_feature_visualizer.py
   ```

### 性能优化

- 使用GPU可显著加速特征提取
- 对于大批量处理，考虑分批处理以避免内存溢出
- 可以修改代码只保存感兴趣的channel范围

## 扩展功能

您可以根据需要修改脚本：

1. **选择特定层**: 修改 `out_indices` 参数
2. **调整可视化**: 修改颜色映射或添加热力图
3. **特征分析**: 添加统计分析功能
4. **批量处理**: 扩展为支持视频帧处理

## 技术细节

- **Backbone**: DINOv2 ViT-Base/14 (14×14 patch size)
- **特征维度**: 768
- **输出层**: 第11层 (最后一层)
- **预处理**: 与DE-ViT项目保持一致
- **可视化**: 线性归一化到0-255范围
