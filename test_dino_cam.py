#!/usr/bin/env python3
"""
测试DINO-CAM分类器并生成CAM可视化
"""

import torch
import torchvision.transforms as transforms
from PIL import Image
import argparse
from pathlib import Path
from dino_cam_classifier import DINO_CAM_Classifier, load_dino_from_devit, visualize_cam_result

def load_trained_model(checkpoint_path, dino_weights, dino_arch, num_classes, cam_dim):
    """加载训练好的模型"""
    
    # 1. 加载DINO backbone
    dino_model, dino_dim = load_dino_from_devit(dino_weights, dino_arch)
    
    # 2. 创建模型
    model = DINO_CAM_Classifier(
        dino_model=dino_model,
        num_classes=num_classes,
        dino_output_dim=dino_dim,
        cam_feature_dim=cam_dim
    )
    
    # 3. 加载训练权重
    checkpoint = torch.load(checkpoint_path, map_location='cpu')
    model.load_state_dict(checkpoint['model_state_dict'])
    
    print(f"✓ Loaded trained model from {checkpoint_path}")
    print(f"✓ Training epoch: {checkpoint['epoch']}")
    print(f"✓ Validation accuracy: {checkpoint['val_acc']:.2f}%")
    
    return model

def preprocess_image(image_path):
    """预处理单张图像"""
    
    transform = transforms.Compose([
        transforms.Resize(256),
        transforms.CenterCrop(224),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], 
                           std=[0.229, 0.224, 0.225])
    ])
    
    image = Image.open(image_path).convert('RGB')
    image_tensor = transform(image).unsqueeze(0)  # 添加batch维度
    
    return image_tensor, image

def test_single_image(model, image_path, class_names=None, target_class=None, save_dir=None):
    """测试单张图像并生成CAM"""
    
    model.eval()
    
    # 预处理图像
    image_tensor, original_image = preprocess_image(image_path)
    
    # 生成CAM
    with torch.no_grad():
        cam, predicted_class, confidence = model.generate_cam(image_tensor, target_class)
    
    # 打印结果
    class_name = class_names[predicted_class] if class_names else f"Class {predicted_class}"
    print(f"\nImage: {image_path}")
    print(f"Predicted class: {predicted_class} ({class_name})")
    print(f"Confidence: {confidence:.4f}")
    print(f"CAM shape: {cam.shape}")
    
    # 可视化
    save_path = None
    if save_dir:
        save_dir = Path(save_dir)
        save_dir.mkdir(exist_ok=True)
        image_name = Path(image_path).stem
        save_path = save_dir / f"{image_name}_cam.png"
    
    visualize_cam_result(
        image=image_tensor,
        cam=cam,
        predicted_class=predicted_class,
        confidence=confidence,
        class_names=class_names,
        save_path=save_path
    )
    
    return predicted_class, confidence, cam

def batch_test_images(model, image_dir, class_names=None, save_dir=None):
    """批量测试图像"""
    
    image_dir = Path(image_dir)
    image_files = []
    
    # 支持的图像格式
    extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
    for ext in extensions:
        image_files.extend(image_dir.glob(f"*{ext}"))
        image_files.extend(image_dir.glob(f"*{ext.upper()}"))
    
    if not image_files:
        print(f"No images found in {image_dir}")
        return
    
    print(f"Found {len(image_files)} images")
    
    results = []
    for image_path in image_files:
        try:
            pred_class, confidence, cam = test_single_image(
                model, image_path, class_names, save_dir=save_dir
            )
            results.append({
                'image': image_path.name,
                'predicted_class': pred_class,
                'confidence': confidence
            })
        except Exception as e:
            print(f"Error processing {image_path}: {e}")
    
    # 打印汇总
    print(f"\n{'='*60}")
    print("Batch Test Results Summary")
    print(f"{'='*60}")
    for result in results:
        class_name = class_names[result['predicted_class']] if class_names else f"Class {result['predicted_class']}"
        print(f"{result['image']:<30} {class_name:<20} {result['confidence']:.4f}")

def main():
    parser = argparse.ArgumentParser(description='Test DINO-CAM Classifier')
    parser.add_argument('--checkpoint', type=str, required=True,
                       help='Path to trained model checkpoint')
    parser.add_argument('--dino-weights', type=str, required=True,
                       help='Path to DINO pretrained weights')
    parser.add_argument('--dino-arch', type=str, default='vit_base',
                       choices=['vit_small', 'vit_base'],
                       help='DINO architecture')
    parser.add_argument('--num-classes', type=int, required=True,
                       help='Number of classes')
    parser.add_argument('--cam-dim', type=int, default=512,
                       help='CAM feature dimension')
    parser.add_argument('--image', type=str,
                       help='Single image path to test')
    parser.add_argument('--image-dir', type=str,
                       help='Directory containing images to test')
    parser.add_argument('--class-names', type=str, nargs='+',
                       help='List of class names')
    parser.add_argument('--target-class', type=int,
                       help='Target class for CAM (default: predicted class)')
    parser.add_argument('--save-dir', type=str, default='cam_results',
                       help='Directory to save CAM visualizations')
    parser.add_argument('--device', type=str, default='cuda',
                       help='Device to use')
    
    args = parser.parse_args()
    
    # 设置设备
    device = torch.device(args.device if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # 加载模型
    print("Loading trained model...")
    model = load_trained_model(
        checkpoint_path=args.checkpoint,
        dino_weights=args.dino_weights,
        dino_arch=args.dino_arch,
        num_classes=args.num_classes,
        cam_dim=args.cam_dim
    ).to(device)
    
    # 处理类别名称
    class_names = args.class_names
    if class_names and len(class_names) != args.num_classes:
        print(f"Warning: {len(class_names)} class names provided, but model has {args.num_classes} classes")
        class_names = None
    
    # 测试图像
    if args.image:
        # 单张图像测试
        print(f"\nTesting single image: {args.image}")
        test_single_image(
            model=model,
            image_path=args.image,
            class_names=class_names,
            target_class=args.target_class,
            save_dir=args.save_dir
        )
    
    elif args.image_dir:
        # 批量图像测试
        print(f"\nTesting images in directory: {args.image_dir}")
        batch_test_images(
            model=model,
            image_dir=args.image_dir,
            class_names=class_names,
            save_dir=args.save_dir
        )
    
    else:
        print("Please specify either --image or --image-dir")
        return
    
    print(f"\n✓ CAM visualizations saved to: {args.save_dir}")

if __name__ == "__main__":
    main()
