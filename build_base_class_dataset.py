#!/usr/bin/env python3
"""
从vitb_shot10.yaml配置中提取基类样本，构建分类数据集
用于训练DINO-CAM分类器
"""

import os
import sys
import json
import shutil
from pathlib import Path
from collections import defaultdict
import argparse
from tqdm import tqdm

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# COCO Few-Shot 2014基类 (来自fs_coco14_base_train数据集)
# 这些是vitb_shot10.yaml中使用的基类
COCO_FEWSHOT_BASE_CLASSES = [
    'person', 'bicycle', 'car', 'motorcycle', 'train', 'truck',
    'boat', 'bench', 'bird', 'horse', 'sheep', 'bear', 'zebra', 'giraffe',
    'backpack', 'handbag', 'suitcase', 'frisbee', 'skis', 'kite', 'surfboard',
    'bottle', 'fork', 'spoon', 'bowl', 'banana', 'apple', 'sandwich', 'orange',
    'broccoli', 'carrot', 'pizza', 'donut', 'chair', 'bed', 'toilet', 'tv',
    'laptop', 'mouse', 'remote', 'microwave', 'oven', 'toaster',
    'refrigerator', 'book', 'clock', 'vase', 'toothbrush'
]

# COCO类别ID到名称的映射
COCO_ID_TO_NAME = {
    1: 'person', 2: 'bicycle', 3: 'car', 4: 'motorcycle', 5: 'airplane',
    6: 'bus', 7: 'train', 8: 'truck', 9: 'boat', 10: 'traffic light',
    11: 'fire hydrant', 13: 'stop sign', 14: 'parking meter', 15: 'bench',
    16: 'bird', 17: 'cat', 18: 'dog', 19: 'horse', 20: 'sheep',
    21: 'cow', 22: 'elephant', 23: 'bear', 24: 'zebra', 25: 'giraffe',
    27: 'backpack', 28: 'umbrella', 31: 'handbag', 32: 'tie', 33: 'suitcase',
    34: 'frisbee', 35: 'skis', 36: 'snowboard', 37: 'sports ball', 38: 'kite',
    39: 'baseball bat', 40: 'baseball glove', 41: 'skateboard', 42: 'surfboard',
    43: 'tennis racket', 44: 'bottle', 46: 'wine glass', 47: 'cup',
    48: 'fork', 49: 'knife', 50: 'spoon', 51: 'bowl', 52: 'banana',
    53: 'apple', 54: 'sandwich', 55: 'orange', 56: 'broccoli', 57: 'carrot',
    58: 'hot dog', 59: 'pizza', 60: 'donut', 61: 'cake', 62: 'chair',
    63: 'couch', 64: 'potted plant', 65: 'bed', 67: 'dining table',
    70: 'toilet', 72: 'tv', 73: 'laptop', 74: 'mouse', 75: 'remote',
    76: 'keyboard', 77: 'cell phone', 78: 'microwave', 79: 'oven',
    80: 'toaster', 81: 'sink', 82: 'refrigerator', 84: 'book',
    85: 'clock', 86: 'vase', 87: 'scissors', 88: 'teddy bear',
    89: 'hair drier', 90: 'toothbrush'
}

def load_coco_annotations(annotation_file):
    """加载COCO标注文件"""
    print(f"Loading COCO annotations from: {annotation_file}")
    
    with open(annotation_file, 'r') as f:
        coco_data = json.load(f)
    
    # 构建类别映射
    categories = {cat['id']: cat['name'] for cat in coco_data['categories']}
    
    # 构建图像信息映射
    images = {img['id']: img for img in coco_data['images']}
    
    # 按图像组织标注
    image_annotations = defaultdict(list)
    for ann in coco_data['annotations']:
        image_annotations[ann['image_id']].append(ann)
    
    print(f"✓ Loaded {len(images)} images, {len(coco_data['annotations'])} annotations")
    print(f"✓ Found {len(categories)} categories")
    
    return images, image_annotations, categories

def filter_base_class_images(images, image_annotations, categories, base_classes):
    """筛选包含基类的图像"""
    print("Filtering images with base classes...")
    
    # 获取基类的ID
    base_class_ids = set()
    for cat_id, cat_name in categories.items():
        if cat_name in base_classes:
            base_class_ids.add(cat_id)
    
    print(f"Base class IDs: {sorted(base_class_ids)}")
    print(f"Base class names: {[categories[cid] for cid in sorted(base_class_ids)]}")
    
    # 筛选图像
    filtered_images = {}
    class_image_count = defaultdict(int)
    
    for img_id, img_info in tqdm(images.items(), desc="Processing images"):
        annotations = image_annotations.get(img_id, [])
        
        # 检查图像是否包含基类
        image_classes = set()
        for ann in annotations:
            if ann['category_id'] in base_class_ids:
                image_classes.add(ann['category_id'])
        
        if image_classes:
            filtered_images[img_id] = {
                'image_info': img_info,
                'classes': image_classes,
                'annotations': annotations
            }
            
            # 统计每个类别的图像数量
            for class_id in image_classes:
                class_image_count[class_id] += 1
    
    print(f"✓ Filtered {len(filtered_images)} images containing base classes")
    
    # 打印类别统计
    print("\nClass distribution:")
    for class_id in sorted(class_image_count.keys()):
        class_name = categories[class_id]
        count = class_image_count[class_id]
        print(f"  {class_name}: {count} images")
    
    return filtered_images, class_image_count

def create_classification_dataset(filtered_images, categories, coco_image_dir, output_dir, 
                                max_images_per_class=None, min_images_per_class=10):
    """创建分类数据集"""
    print(f"\nCreating classification dataset in: {output_dir}")
    
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    # 创建类别目录
    class_dirs = {}
    for class_id, class_name in categories.items():
        if class_name in COCO_FEWSHOT_BASE_CLASSES:
            class_dir = output_path / class_name
            class_dir.mkdir(exist_ok=True)
            class_dirs[class_id] = class_dir
    
    # 按类别组织图像
    class_images = defaultdict(list)
    for img_id, img_data in filtered_images.items():
        for class_id in img_data['classes']:
            if class_id in class_dirs:
                class_images[class_id].append(img_data['image_info'])
    
    # 复制图像文件
    copy_stats = {}
    total_copied = 0
    
    for class_id, images in class_images.items():
        class_name = categories[class_id]
        class_dir = class_dirs[class_id]
        
        # 限制每个类别的图像数量
        if max_images_per_class and len(images) > max_images_per_class:
            images = images[:max_images_per_class]
        
        # 检查最小图像数量
        if len(images) < min_images_per_class:
            print(f"⚠️  Class '{class_name}' has only {len(images)} images (min: {min_images_per_class})")
        
        copied_count = 0
        for img_info in tqdm(images, desc=f"Copying {class_name}"):
            src_path = Path(coco_image_dir) / img_info['file_name']
            dst_path = class_dir / img_info['file_name']
            
            if src_path.exists():
                if not dst_path.exists():
                    shutil.copy2(src_path, dst_path)
                copied_count += 1
            else:
                print(f"⚠️  Image not found: {src_path}")
        
        copy_stats[class_name] = copied_count
        total_copied += copied_count
        print(f"✓ {class_name}: {copied_count} images")
    
    print(f"\n✓ Total images copied: {total_copied}")
    
    # 保存数据集信息
    dataset_info = {
        'base_classes': COCO_FEWSHOT_BASE_CLASSES,
        'class_stats': copy_stats,
        'total_images': total_copied,
        'source_config': 'vitb_shot10.yaml',
        'source_dataset': 'fs_coco14_base_train'
    }
    
    info_file = output_path / 'dataset_info.json'
    with open(info_file, 'w') as f:
        json.dump(dataset_info, f, indent=2)
    
    print(f"✓ Dataset info saved to: {info_file}")
    
    return copy_stats

def create_train_val_split(dataset_dir, train_ratio=0.8):
    """创建训练/验证集划分"""
    print(f"\nCreating train/val split (train ratio: {train_ratio})")
    
    dataset_path = Path(dataset_dir)
    train_dir = dataset_path / 'train'
    val_dir = dataset_path / 'val'
    
    train_dir.mkdir(exist_ok=True)
    val_dir.mkdir(exist_ok=True)
    
    # 处理每个类别
    for class_dir in dataset_path.iterdir():
        if class_dir.is_dir() and class_dir.name not in ['train', 'val']:
            class_name = class_dir.name
            
            # 获取所有图像
            images = list(class_dir.glob('*.jpg')) + list(class_dir.glob('*.png'))
            
            if not images:
                continue
            
            # 划分训练/验证集
            num_train = int(len(images) * train_ratio)
            train_images = images[:num_train]
            val_images = images[num_train:]
            
            # 创建类别目录
            train_class_dir = train_dir / class_name
            val_class_dir = val_dir / class_name
            train_class_dir.mkdir(exist_ok=True)
            val_class_dir.mkdir(exist_ok=True)
            
            # 移动图像
            for img in train_images:
                shutil.move(str(img), str(train_class_dir / img.name))
            
            for img in val_images:
                shutil.move(str(img), str(val_class_dir / img.name))
            
            print(f"  {class_name}: {len(train_images)} train, {len(val_images)} val")
            
            # 删除原始类别目录
            if class_dir.exists():
                shutil.rmtree(class_dir)
    
    print("✓ Train/val split completed")

def main():
    parser = argparse.ArgumentParser(description='Build base class dataset from vitb_shot10.yaml')
    parser.add_argument('--coco-dir', type=str, required=True,
                       help='Path to COCO dataset directory')
    parser.add_argument('--annotation-file', type=str,
                       help='Path to COCO annotation file (default: auto-detect)')
    parser.add_argument('--output-dir', type=str, default='./base_class_dataset',
                       help='Output directory for classification dataset')
    parser.add_argument('--max-images-per-class', type=int, default=1000,
                       help='Maximum images per class')
    parser.add_argument('--min-images-per-class', type=int, default=10,
                       help='Minimum images per class')
    parser.add_argument('--train-ratio', type=float, default=0.8,
                       help='Training set ratio for train/val split')
    parser.add_argument('--create-split', action='store_true',
                       help='Create train/val split')
    
    args = parser.parse_args()
    
    # 检查COCO目录
    coco_dir = Path(args.coco_dir)
    if not coco_dir.exists():
        print(f"Error: COCO directory not found: {coco_dir}")
        return 1
    
    # 自动检测标注文件
    if args.annotation_file is None:
        # 尝试常见的标注文件路径
        possible_annotations = [
            coco_dir / 'annotations' / 'instances_train2014.json',
            coco_dir / 'annotations' / 'instances_train2017.json',
            coco_dir / 'annotations' / 'fs_coco14_base_train.json'
        ]
        
        annotation_file = None
        for ann_file in possible_annotations:
            if ann_file.exists():
                annotation_file = ann_file
                break
        
        if annotation_file is None:
            print("Error: Could not find annotation file. Please specify --annotation-file")
            return 1
    else:
        annotation_file = args.annotation_file
    
    # 检测图像目录
    possible_image_dirs = [
        coco_dir / 'train2014',
        coco_dir / 'train2017',
        coco_dir / 'images' / 'train2014',
        coco_dir / 'images' / 'train2017'
    ]
    
    image_dir = None
    for img_dir in possible_image_dirs:
        if img_dir.exists():
            image_dir = img_dir
            break
    
    if image_dir is None:
        print("Error: Could not find image directory")
        return 1
    
    print(f"Using annotation file: {annotation_file}")
    print(f"Using image directory: {image_dir}")
    print(f"Base classes from vitb_shot10.yaml: {len(COCO_FEWSHOT_BASE_CLASSES)} classes")
    print(f"Classes: {COCO_FEWSHOT_BASE_CLASSES}")
    
    try:
        # 1. 加载COCO标注
        images, image_annotations, categories = load_coco_annotations(annotation_file)
        
        # 2. 筛选基类图像
        filtered_images, class_stats = filter_base_class_images(
            images, image_annotations, categories, COCO_FEWSHOT_BASE_CLASSES
        )
        
        # 3. 创建分类数据集
        copy_stats = create_classification_dataset(
            filtered_images, categories, image_dir, args.output_dir,
            args.max_images_per_class, args.min_images_per_class
        )
        
        # 4. 可选：创建训练/验证集划分
        if args.create_split:
            create_train_val_split(args.output_dir, args.train_ratio)
        
        print(f"\n🎉 Base class dataset created successfully!")
        print(f"Output directory: {args.output_dir}")
        print(f"Classes: {len(copy_stats)}")
        print(f"Total images: {sum(copy_stats.values())}")
        
        print(f"\nTo train DINO-CAM classifier:")
        print(f"python train_dino_cam.py \\")
        print(f"    --data-dir {args.output_dir} \\")
        print(f"    --dino-weights path/to/dino_weights.pth \\")
        print(f"    --num-classes {len(copy_stats)}")
        
        return 0
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
