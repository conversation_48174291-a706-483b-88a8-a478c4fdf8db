_BASE_: "../../Base-RCNN-C4.yaml"
DE:
  CLASS_PROTOTYPES: "weights/initial/open-vocabulary/prototypes/coco/class_prototypes_base.vitb14.pth,weights/initial/open-vocabulary/prototypes/coco/class_prototypes_novel.vitb14.pth"
  BG_PROTOTYPES: "weights/initial/background/background_prototypes.vitb14.pth"
  BG_CLS_LOSS_WEIGHT: 0.2
  TOPK: 10

MODEL:
  META_ARCHITECTURE: "OpenSetDetectorWithExamples"
  BACKBONE:
    NAME: "build_dino_v2_vit"
    TYPE: "base"
  WEIGHTS: "" 
  MASK_ON: False
  RPN:
    HEAD_NAME: StandardRPNHead
    IN_FEATURES: ["res4"]
  ROI_HEADS:
    SCORE_THRESH_TEST: 0.001
  ROI_BOX_HEAD:
    NAME: ""
    NUM_FC: 0
    POOLER_RESOLUTION: 7
    CLS_AGNOSTIC_BBOX_REG: True
  PIXEL_MEAN: [0.48145466, 0.4578275, 0.40821073]
  PIXEL_STD: [0.26862954, 0.26130258, 0.27577711]
INPUT:
  MIN_SIZE_TRAIN: (640, 672, 704, 736, 768, 800)
DATASETS:
  TRAIN: ("coco_2017_ovd_b_train",)
  TEST: ("coco_2017_ovd_all_test",)
TEST:
  EVAL_PERIOD: 5000
SOLVER:
  IMS_PER_BATCH: 16
  BASE_LR: 0.002
  STEPS: (60000, 80000)
  MAX_ITER: 90000
  WARMUP_ITERS: 5000
  CHECKPOINT_PERIOD: 5000

INPUT:
  MIN_SIZE_TRAIN_SAMPLING: choice
  MIN_SIZE_TRAIN: (640, 672, 704, 736, 768, 800)
  MAX_SIZE_TRAIN: 1333
  MIN_SIZE_TEST: 800
  MAX_SIZE_TEST: 1333
  FORMAT: "RGB"
