class Trainer(DefaultTrainer):  # 定义一个名为 Trainer 的类，它继承自 Detectron2 的 DefaultTrainer 类
    """
    We use the "DefaultTrainer" which contains pre-defined default logic for
    standard training workflow. They may not work for you, especially if you
    are working on a new research project. In that case you can write your
    own training loop. You can use "tools/plain_train_net.py" as an example.
    (我们使用“DefaultTrainer”，它包含标准训练流程的预定义默认逻辑。
    它们可能不适用于您，特别是如果您正在进行新的研究项目。
    在这种情况下，您可以编写自己的训练循环。您可以使用“tools/plain_train_net.py”作为示例。)
    """

    @classmethod  # 这是一个类方法，可以通过类名直接调用 (例如 Trainer.build_evaluator(...))
    def build_evaluator(cls, cfg, dataset_name, output_folder=None):
        """
        Create evaluator(s) for a given dataset.
        This uses the special metadata "evaluator_type" associated with each builtin dataset.
        For your own dataset, you can simply create an evaluator manually in your
        script and do not have to worry about the hacky if-else logic here.
        (为给定数据集创建一个或多个评估器。
        这使用了与每个内置数据集关联的特殊元数据 "evaluator_type"。
        对于您自己的数据集，您可以简单地在脚本中手动创建评估器，而不必担心这里的取巧的 if-else 逻辑。)
        """
        # 如果未指定输出文件夹，则在配置的输出目录下创建一个名为 "inference" 的子文件夹
        if output_folder is None:
            output_folder = os.path.join(cfg.OUTPUT_DIR, "inference")

        evaluator_list = []  # 初始化一个空列表，用于存放将要创建的评估器实例

        # --- DE-ViT/OpenSet 模型特定的评估器构建逻辑 ---
        # 检查配置文件中指定的模型元架构是否包含 "OpenSet"
        # 这表明当前模型是为开放集合检测或少样本检测设计的 (如 DE-ViT)
        if 'OpenSet' in cfg.MODEL.META_ARCHITECTURE:
            if 'lvis' in dataset_name.lower():  # 如果数据集名称包含 'lvis' (不区分大小写)
                # 为 LVIS 数据集创建一个 LVISEvaluator
                evaluator_list.append(LVISEvaluator(dataset_name, output_dir=output_folder))
            else:  # 对于非 LVIS 的开放集合设置 (主要针对 COCO 和 VOC)
                if 'voc' in dataset_name.lower():  # 如果数据集名称包含 'voc' (不区分大小写)
                    # 为 PASCAL VOC 数据集创建一个自定义的 PascalVOCDetectionEvaluator
                    # 这个评估器可能在 lib.voc_eval 中定义，以支持 VOC 的评估标准
                    evaluator_list.append(PascalVOCDetectionEvaluator(dataset_name))
                else:  # 默认为 COCO 数据集的少样本评估
                    # 获取训练时使用的数据集名称 (通常是基础类别数据集)
                    dtrain_name = cfg.DATASETS.TRAIN[0]
                    # 从预定义的字典中加载基础类别 (seen_cnames) 和所有类别 (all_cnames) 的名称列表
                    # SEEN_CLS_DICT 和 ALL_CLS_DICT 可能在 lib.categories 中定义
                    seen_cnames = SEEN_CLS_DICT[dtrain_name]
                    all_cnames = ALL_CLS_DICT[dtrain_name]
                    # 推导出新类别 (unseen_cnames) 的名称列表
                    unseen_cnames = [c for c in all_cnames if c not in seen_cnames]
                    # 创建一个 COCOEvaluator，并特别设置:
                    #  few_shot_mode=True: 告知评估器当前是少样本评估模式
                    #  seen_cnames, unseen_cnames, all_cnames: 传入类别划分信息，
                    #                                       以便评估器可以分别计算基础类和新类的性能指标 (如bAP, nAP)
                    evaluator_list.append(COCOEvaluator(dataset_name, output_dir=output_folder, few_shot_mode=True,
                                                        seen_cnames=seen_cnames, unseen_cnames=unseen_cnames,
                                                        all_cnames=all_cnames))
            # 如果 evaluator_list 不为空，则返回一个 DatasetEvaluators 对象 (可以包含一个或多个评估器)
            # DatasetEvaluators 会依次调用列表中的每个评估器
            if len(evaluator_list) == 0:  # 理论上对于OpenSet应该总能匹配到一种
                raise NotImplementedError(f"No evaluator found for OpenSet dataset {dataset_name}")
            return DatasetEvaluators(evaluator_list)

        # --- Detectron2 标准的评估器构建逻辑 (如果不是 OpenSet 模型) ---
        # 从数据集的元数据中获取预定义的 "evaluator_type"
        evaluator_type = MetadataCatalog.get(dataset_name).evaluator_type

        # 根据 evaluator_type 创建相应的标准评估器
        if evaluator_type in ["sem_seg", "coco_panoptic_seg"]:  # 语义分割或全景分割
            evaluator_list.append(
                SemSegEvaluator(
                    dataset_name,
                    distributed=True,  # 是否进行分布式评估
                    output_dir=output_folder,
                )
            )
        if evaluator_type in ["coco", "coco_panoptic_seg"]:  # COCO 目标检测或全景分割
            evaluator_list.append(COCOEvaluator(dataset_name, output_dir=output_folder))
        if evaluator_type == "coco_panoptic_seg":  # COCO 全景分割的特定部分
            evaluator_list.append(COCOPanopticEvaluator(dataset_name, output_folder))
        if evaluator_type == "cityscapes_instance":  # Cityscapes 实例分割
            assert (
                    torch.cuda.device_count() >= comm.get_rank()  # CityscapesEvaluator 当前不支持多机评估
            ), "CityscapesEvaluator currently do not work with multiple machines."
            return CityscapesInstanceEvaluator(dataset_name)
        if evaluator_type == "cityscapes_sem_seg":  # Cityscapes 语义分割
            assert (
                    torch.cuda.device_count() >= comm.get_rank()
            ), "CityscapesEvaluator currently do not work with multiple machines."
            return CityscapesSemSegEvaluator(dataset_name)
        elif evaluator_type == "pascal_voc":  # PASCAL VOC 目标检测 (标准评估器)
            return PascalVOCDetectionEvaluator(dataset_name)  # 注意这里也可能是自定义的，取决于 lib.voc_eval
        elif evaluator_type == "lvis":  # LVIS 数据集评估
            return LVISEvaluator(dataset_name, output_dir=output_folder)

        # --- 返回最终的评估器 ---
        if len(evaluator_list) == 0:  # 如果没有找到合适的评估器
            raise NotImplementedError(
                "no Evaluator for the dataset {} with the type {}".format(
                    dataset_name, evaluator_type
                )
            )
        elif len(evaluator_list) == 1:  # 如果只有一个评估器
            return evaluator_list[0]  # 直接返回该评估器实例
        return DatasetEvaluators(evaluator_list)  # 如果有多个评估器，则用 DatasetEvaluators 包装后返回

    @classmethod  # 这是一个类方法
    def test_with_TTA(cls, cfg, model):  # 使用测试时增强 (Test-Time Augmentation, TTA) 进行测试
        logger = logging.getLogger("detectron2.trainer")  # 获取日志记录器
        # 在训练结束时，使用 TTA 运行一次评估
        # 注意：仅支持某些 R-CNN 类型的模型
        logger.info("Running inference with test-time augmentation ...")  # 记录日志信息

        # 使用 GeneralizedRCNNWithTTA 类包装原始模型，以实现 TTA 功能
        model_tta = GeneralizedRCNNWithTTA(cfg, model, tta_mapper=None, batch_size=1)  # Detectron2 v0.5+ TTA API
        # (旧版 Detectron2 TTA 调用可能不同，如 model = GeneralizedRCNNWithTTA(cfg, model))

        # 为测试集中的每个数据集名称构建评估器列表，输出文件夹指定为 "inference_TTA"
        evaluators = [
            cls.build_evaluator(  # 调用上面自定义的 build_evaluator 方法
                cfg, name, output_folder=os.path.join(cfg.OUTPUT_DIR, "inference_TTA")
            )
            for name in cfg.DATASETS.TEST  # 遍历配置文件中定义的所有测试数据集
        ]
        # 调用基类的 test 方法 (DefaultTrainer.test) 执行 TTA 评估
        res = cls.test(cfg, model_tta, evaluators)  # 注意这里传入的是 model_tta
        # 在结果字典的每个键后面添加 "_TTA" 后缀，以区分 TTA 结果和非 TTA 结果
        res = OrderedDict({k + "_TTA": v for k, v in res.items()})
        return res  # 返回 TTA 评估结果