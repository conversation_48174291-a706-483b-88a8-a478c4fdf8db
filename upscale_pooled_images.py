#!/usr/bin/env python3
"""
将7x7的池化图像放大，每个像素用100x100的像素块展示
"""

import os
import sys
import cv2
import numpy as np
from pathlib import Path
import argparse
from tqdm import tqdm
import glob

def upscale_7x7_image(image_path, block_size=100, interpolation='nearest'):
    """
    将7x7图像放大，每个像素用block_size x block_size的像素块展示
    
    Args:
        image_path: 输入图像路径
        block_size: 每个像素对应的像素块大小 (默认100x100)
        interpolation: 插值方法 ('nearest', 'linear', 'cubic')
        
    Returns:
        numpy.ndarray: 放大后的图像 (700x700 if block_size=100)
    """
    # 读取7x7图像
    image = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)
    
    if image is None:
        raise ValueError(f"Cannot read image: {image_path}")
    
    # 验证图像尺寸
    if image.shape != (7, 7):
        print(f"Warning: Image {image_path} is not 7x7, actual size: {image.shape}")
        # 如果不是7x7，先调整到7x7
        image = cv2.resize(image, (7, 7), interpolation=cv2.INTER_NEAREST)
    
    # 计算输出尺寸
    output_size = 7 * block_size
    
    # 选择插值方法
    if interpolation == 'nearest':
        cv2_interp = cv2.INTER_NEAREST
    elif interpolation == 'linear':
        cv2_interp = cv2.INTER_LINEAR
    elif interpolation == 'cubic':
        cv2_interp = cv2.INTER_CUBIC
    else:
        cv2_interp = cv2.INTER_NEAREST
    
    # 放大图像
    upscaled = cv2.resize(image, (output_size, output_size), interpolation=cv2_interp)
    
    return upscaled

def upscale_with_grid(image_path, block_size=100, grid_color=128, grid_width=1):
    """
    将7x7图像放大并添加网格线，清楚显示每个原始像素的边界
    
    Args:
        image_path: 输入图像路径
        block_size: 每个像素对应的像素块大小
        grid_color: 网格线颜色 (0-255)
        grid_width: 网格线宽度
        
    Returns:
        numpy.ndarray: 带网格的放大图像
    """
    # 读取7x7图像
    image = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)
    
    if image is None:
        raise ValueError(f"Cannot read image: {image_path}")
    
    # 验证并调整尺寸
    if image.shape != (7, 7):
        print(f"Warning: Resizing {image.shape} to (7, 7)")
        image = cv2.resize(image, (7, 7), interpolation=cv2.INTER_NEAREST)
    
    # 创建放大图像
    output_size = 7 * block_size
    upscaled = np.zeros((output_size, output_size), dtype=np.uint8)
    
    # 填充每个像素块
    for i in range(7):
        for j in range(7):
            pixel_value = image[i, j]
            
            # 计算像素块的位置
            start_y = i * block_size
            end_y = start_y + block_size
            start_x = j * block_size
            end_x = start_x + block_size
            
            # 填充像素块
            upscaled[start_y:end_y, start_x:end_x] = pixel_value
    
    # 添加网格线
    if grid_width > 0:
        # 垂直线
        for i in range(1, 7):
            x = i * block_size
            upscaled[:, x:x+grid_width] = grid_color
        
        # 水平线
        for i in range(1, 7):
            y = i * block_size
            upscaled[y:y+grid_width, :] = grid_color
    
    return upscaled

def create_comparison_image(original_path, upscaled_image, block_size=100):
    """
    创建对比图像，显示原始7x7图像和放大后的图像
    
    Args:
        original_path: 原始图像路径
        upscaled_image: 放大后的图像
        block_size: 像素块大小
        
    Returns:
        numpy.ndarray: 对比图像
    """
    # 读取原始图像
    original = cv2.imread(original_path, cv2.IMREAD_GRAYSCALE)
    if original.shape != (7, 7):
        original = cv2.resize(original, (7, 7), interpolation=cv2.INTER_NEAREST)
    
    # 将原始7x7图像放大到与upscaled_image相同的高度用于对比
    original_display_size = min(200, block_size * 7 // 4)  # 适当的显示尺寸
    original_display = cv2.resize(original, (original_display_size, original_display_size), 
                                 interpolation=cv2.INTER_NEAREST)
    
    # 创建对比图像
    upscaled_h, upscaled_w = upscaled_image.shape
    gap = 20  # 图像间的间隔
    
    # 计算总宽度
    total_width = original_display_size + gap + upscaled_w
    total_height = max(original_display_size, upscaled_h)
    
    # 创建白色背景
    comparison = np.ones((total_height, total_width), dtype=np.uint8) * 255
    
    # 放置原始图像 (居中)
    orig_y_offset = (total_height - original_display_size) // 2
    comparison[orig_y_offset:orig_y_offset + original_display_size, 
              :original_display_size] = original_display
    
    # 放置放大图像
    upscaled_y_offset = (total_height - upscaled_h) // 2
    comparison[upscaled_y_offset:upscaled_y_offset + upscaled_h, 
              original_display_size + gap:] = upscaled_image
    
    return comparison

def process_single_image(input_path, output_dir, block_size=100, add_grid=True, 
                        create_comparison=True, grid_color=128, grid_width=1):
    """
    处理单张7x7图像
    
    Args:
        input_path: 输入图像路径
        output_dir: 输出目录
        block_size: 像素块大小
        add_grid: 是否添加网格
        create_comparison: 是否创建对比图
        grid_color: 网格颜色
        grid_width: 网格宽度
        
    Returns:
        bool: 处理是否成功
    """
    try:
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        input_file = Path(input_path)
        base_name = input_file.stem
        
        # 生成放大图像
        if add_grid:
            upscaled = upscale_with_grid(input_path, block_size, grid_color, grid_width)
            suffix = f"_upscaled_{block_size}x{block_size}_grid"
        else:
            upscaled = upscale_7x7_image(input_path, block_size, 'nearest')
            suffix = f"_upscaled_{block_size}x{block_size}"
        
        # 保存放大图像
        upscaled_path = output_path / f"{base_name}{suffix}.png"
        cv2.imwrite(str(upscaled_path), upscaled)
        
        # 创建对比图像
        if create_comparison:
            comparison = create_comparison_image(input_path, upscaled, block_size)
            comparison_path = output_path / f"{base_name}_comparison.png"
            cv2.imwrite(str(comparison_path), comparison)
        
        return True
        
    except Exception as e:
        print(f"Error processing {input_path}: {e}")
        return False

def batch_process_images(input_dir, output_dir, block_size=100, add_grid=True,
                        create_comparison=True, extensions=None):
    """
    批量处理7x7图像
    
    Args:
        input_dir: 输入目录
        output_dir: 输出目录
        block_size: 像素块大小
        add_grid: 是否添加网格
        create_comparison: 是否创建对比图
        extensions: 支持的文件扩展名
        
    Returns:
        tuple: (成功数量, 总数量)
    """
    if extensions is None:
        extensions = ['.png', '.jpg', '.jpeg', '.bmp', '.tiff']
    
    # 获取所有图像文件
    input_path = Path(input_dir)
    image_files = []
    
    for ext in extensions:
        image_files.extend(input_path.glob(f"*{ext}"))
        image_files.extend(input_path.glob(f"*{ext.upper()}"))
    
    image_files = sorted(list(set(image_files)))
    
    if not image_files:
        print(f"No image files found in {input_dir}")
        return 0, 0
    
    print(f"Found {len(image_files)} images to process")
    print(f"Block size: {block_size}x{block_size}")
    print(f"Output size per image: {7*block_size}x{7*block_size}")
    print(f"Grid: {'Yes' if add_grid else 'No'}")
    print(f"Comparison: {'Yes' if create_comparison else 'No'}")
    print()
    
    # 处理每张图像
    success_count = 0
    
    for image_file in tqdm(image_files, desc="Upscaling images"):
        if process_single_image(image_file, output_dir, block_size, add_grid, 
                               create_comparison):
            success_count += 1
    
    return success_count, len(image_files)

def main():
    parser = argparse.ArgumentParser(
        description='Upscale 7x7 pooled images with pixel blocks',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # 基本使用 (100x100像素块)
  python upscale_pooled_images.py --input pooled_7x7 --output upscaled_700x700

  # 自定义像素块大小
  python upscale_pooled_images.py --input pooled_7x7 --output upscaled --block-size 50

  # 不添加网格线
  python upscale_pooled_images.py --input pooled_7x7 --output upscaled --no-grid

  # 处理单张图像
  python upscale_pooled_images.py --input image_7x7.png --output upscaled_image.png
        """
    )
    
    parser.add_argument('--input', '-i', type=str, required=True,
                       help='Input directory or single image file (7x7 images)')
    parser.add_argument('--output', '-o', type=str, required=True,
                       help='Output directory or single image file')
    parser.add_argument('--block-size', type=int, default=100,
                       help='Size of each pixel block (default: 100)')
    parser.add_argument('--no-grid', action='store_true',
                       help='Do not add grid lines')
    parser.add_argument('--no-comparison', action='store_true',
                       help='Do not create comparison images')
    parser.add_argument('--grid-color', type=int, default=128,
                       help='Grid line color (0-255, default: 128)')
    parser.add_argument('--grid-width', type=int, default=1,
                       help='Grid line width (default: 1)')
    parser.add_argument('--extensions', nargs='+',
                       default=['.png', '.jpg', '.jpeg', '.bmp'],
                       help='Supported file extensions')
    
    args = parser.parse_args()
    
    # 检查输入路径
    input_path = Path(args.input)
    if not input_path.exists():
        print(f"Error: Input path {args.input} does not exist!")
        return 1
    
    # 参数验证
    if args.block_size <= 0:
        print("Error: Block size must be positive!")
        return 1
    
    if not (0 <= args.grid_color <= 255):
        print("Error: Grid color must be between 0 and 255!")
        return 1
    
    try:
        if input_path.is_file():
            # 处理单张图像
            print("Processing single image...")
            print(f"Input: {args.input}")
            print(f"Output: {args.output}")
            print(f"Block size: {args.block_size}x{args.block_size}")
            
            # 确保输出目录存在
            output_path = Path(args.output)
            if output_path.suffix:  # 如果是文件路径
                output_dir = output_path.parent
                output_dir.mkdir(parents=True, exist_ok=True)
            else:  # 如果是目录路径
                output_path.mkdir(parents=True, exist_ok=True)
            
            success = process_single_image(
                input_path, 
                args.output if not output_path.suffix else output_path.parent,
                args.block_size,
                not args.no_grid,
                not args.no_comparison,
                args.grid_color,
                args.grid_width
            )
            
            if success:
                final_size = 7 * args.block_size
                print(f"✓ Image upscaled successfully to {final_size}x{final_size}!")
                return 0
            else:
                print("✗ Failed to process image!")
                return 1
                
        elif input_path.is_dir():
            # 批量处理目录
            print("Processing directory...")
            
            success_count, total_count = batch_process_images(
                args.input,
                args.output,
                args.block_size,
                not args.no_grid,
                not args.no_comparison,
                args.extensions
            )
            
            print(f"\nProcessing complete!")
            print(f"Successfully processed: {success_count}/{total_count} images")
            
            if success_count > 0:
                final_size = 7 * args.block_size
                print(f"✓ Images upscaled to {final_size}x{final_size} pixels!")
                
            if success_count == total_count:
                print("✓ All images processed successfully!")
                return 0
            else:
                print(f"⚠️  {total_count - success_count} images failed to process")
                return 1
        else:
            print(f"Error: {args.input} is neither a file nor a directory!")
            return 1
            
    except KeyboardInterrupt:
        print("\nProcessing interrupted by user")
        return 1
    except Exception as e:
        print(f"Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
