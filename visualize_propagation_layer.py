#!/usr/bin/env python3
"""
可视化DE-ViT模型第一个传播层的卷积输出
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import torch
import numpy as np
import cv2
import matplotlib.pyplot as plt
from pathlib import Path
import argparse
from detectron2.config import get_cfg
import detectron2.data.transforms as T
import detectron2.data.detection_utils as utils
from tools.train_net import Trainer
from PIL import Image
import seaborn as sns

class FeatureHook:
    """用于捕获中间层特征的Hook类"""
    def __init__(self):
        self.features = None
        self.layer_name = None
    
    def hook_fn(self, module, input, output):
        """Hook函数，保存特征"""
        self.features = output.detach().cpu()
        print(f"Captured features from {self.layer_name}: {output.shape}")

def setup_config(config_file, model_path, device='cuda'):
    """设置配置"""
    config = get_cfg()
    config.merge_from_file(config_file)
    
    # 基本设置
    config.MODEL.DEVICE = device
    config.MODEL.WEIGHTS = model_path
    config.MODEL.MASK_ON = True
    
    # Few-shot设置
    config.MODEL.ROI_HEADS.NUM_CLASSES = 80  # COCO类别数
    
    config.freeze()
    return config

def register_hooks(model):
    """为第一个传播层注册hook"""
    hooks = []
    feature_hooks = []
    
    # 查找PropagateNet实例
    for name, module in model.named_modules():
        if 'per_cls_cnn' in name and hasattr(module, 'main_layers'):
            # 找到第一个传播层的卷积部分
            first_layer = module.main_layers[0]  # 第一个传播层
            for layer_name, layer in first_layer.named_children():
                if isinstance(layer, torch.nn.Conv2d):
                    print(f"Registering hook for: {name}.main_layers[0].{layer_name}")
                    
                    # 创建hook
                    feature_hook = FeatureHook()
                    feature_hook.layer_name = f"{name}.main_layers[0].{layer_name}"
                    
                    # 注册hook
                    hook = layer.register_forward_hook(feature_hook.hook_fn)
                    hooks.append(hook)
                    feature_hooks.append(feature_hook)
                    break  # 只要第一个Conv2d
            break  # 只要第一个PropagateNet
    
    return hooks, feature_hooks

def load_model_and_setup_hooks(config, model_path, device='cuda'):
    """加载模型并设置hooks"""
    print(f"Loading model from: {model_path}")
    
    # 构建模型
    model = Trainer.build_model(config).to(device)
    
    # 加载权重
    checkpoint = torch.load(model_path, map_location=device)
    model.load_state_dict(checkpoint['model'])
    model.eval()
    
    # 注册hooks
    hooks, feature_hooks = register_hooks(model)
    
    print("✓ Model loaded and hooks registered!")
    return model, hooks, feature_hooks

def preprocess_image(image_path, config):
    """预处理图像"""
    print(f"Processing image: {image_path}")
    
    # 读取图像
    image = utils.read_image(image_path, format="RGB")
    height, width = image.shape[0], image.shape[1]
    
    print(f"Original image size: {width}x{height}")
    
    # 构建数据增强
    augs = utils.build_augmentation(config, False)
    augmentations = T.AugmentationList(augs)
    
    # 应用增强
    aug_input = T.AugInput(image)
    augmentations(aug_input)
    
    # 准备输入数据
    dataset_dict = {
        "height": height,
        "width": width,
        "image": torch.as_tensor(np.ascontiguousarray(aug_input.image.transpose(2, 0, 1)))
    }
    
    return [dataset_dict], image

def run_inference_with_hooks(model, batched_inputs, feature_hooks, device='cuda'):
    """运行推理并捕获特征"""
    print("Running inference with feature capture...")
    
    # 移动数据到设备
    for item in batched_inputs:
        item["image"] = item["image"].to(device)
    
    # 推理
    with torch.no_grad():
        outputs = model(batched_inputs)
    
    # 检查是否捕获到特征
    captured_features = []
    for hook in feature_hooks:
        if hook.features is not None:
            captured_features.append({
                'layer_name': hook.layer_name,
                'features': hook.features
            })
            print(f"✓ Captured features from {hook.layer_name}: {hook.features.shape}")
        else:
            print(f"✗ No features captured from {hook.layer_name}")
    
    return outputs, captured_features

def visualize_conv_features(features, layer_name, output_dir, max_channels=16):
    """可视化卷积特征"""
    print(f"Visualizing features from {layer_name}")
    
    # features shape: [batch, channels, height, width]
    if len(features.shape) != 4:
        print(f"Unexpected feature shape: {features.shape}")
        return
    
    batch_size, num_channels, height, width = features.shape
    print(f"Feature shape: {features.shape}")
    
    # 只可视化第一个batch和前max_channels个通道
    features = features[0]  # [channels, height, width]
    num_channels = min(num_channels, max_channels)
    
    # 创建输出目录
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    # 计算网格布局
    cols = 4
    rows = (num_channels + cols - 1) // cols
    
    # 创建大图
    fig, axes = plt.subplots(rows, cols, figsize=(cols*3, rows*3))
    if rows == 1:
        axes = axes.reshape(1, -1)
    
    for i in range(num_channels):
        row = i // cols
        col = i % cols
        
        # 获取特征图
        feature_map = features[i].numpy()
        
        # 归一化到0-1
        if feature_map.max() > feature_map.min():
            feature_map = (feature_map - feature_map.min()) / (feature_map.max() - feature_map.min())
        
        # 显示
        im = axes[row, col].imshow(feature_map, cmap='viridis')
        axes[row, col].set_title(f'Channel {i}')
        axes[row, col].axis('off')
        plt.colorbar(im, ax=axes[row, col], fraction=0.046, pad=0.04)
    
    # 隐藏多余的子图
    for i in range(num_channels, rows * cols):
        row = i // cols
        col = i % cols
        axes[row, col].axis('off')
    
    plt.tight_layout()
    
    # 保存大图
    safe_layer_name = layer_name.replace('.', '_').replace('/', '_')
    output_file = output_path / f"{safe_layer_name}_features.png"
    plt.savefig(output_file, dpi=150, bbox_inches='tight')
    plt.close()
    
    print(f"✓ Feature visualization saved to: {output_file}")
    
    # 保存单独的通道图像
    channel_dir = output_path / f"{safe_layer_name}_channels"
    channel_dir.mkdir(exist_ok=True)
    
    for i in range(num_channels):
        feature_map = features[i].numpy()
        
        # 归一化到0-255
        if feature_map.max() > feature_map.min():
            feature_map = (feature_map - feature_map.min()) / (feature_map.max() - feature_map.min())
        feature_map = (feature_map * 255).astype(np.uint8)
        
        # 保存
        channel_file = channel_dir / f"channel_{i:03d}.png"
        cv2.imwrite(str(channel_file), feature_map)
    
    print(f"✓ Individual channels saved to: {channel_dir}")

def cleanup_hooks(hooks):
    """清理hooks"""
    for hook in hooks:
        hook.remove()
    print("✓ Hooks cleaned up")

def main():
    parser = argparse.ArgumentParser(description='Visualize DE-ViT propagation layer features')
    parser.add_argument('--image', type=str, required=True, help='Path to input image')
    parser.add_argument('--config', type=str, 
                       default='configs/few-shot/vitb_shot10.yaml',
                       help='Path to config file')
    parser.add_argument('--model', type=str,
                       help='Path to model weights')
    parser.add_argument('--output', type=str, default='propagation_features',
                       help='Output directory for visualizations')
    parser.add_argument('--device', type=str, default='cuda',
                       choices=['cuda', 'cpu'], help='Device to use')
    parser.add_argument('--max-channels', type=int, default=16,
                       help='Maximum number of channels to visualize')
    
    args = parser.parse_args()
    
    # 检查输入文件
    if not os.path.exists(args.image):
        print(f"Error: Image file {args.image} not found!")
        return 1
    
    # 自动检测模型权重
    if args.model is None:
        model_pattern = "weights/trained/few-shot/coco/vitb_shot10_*.pth"
        import glob
        model_files = glob.glob(model_pattern)
        if not model_files:
            print(f"Error: No model weights found matching {model_pattern}")
            return 1
        args.model = model_files[0]
        print(f"Auto-detected model: {args.model}")
    
    # 检查设备
    if args.device == 'cuda' and not torch.cuda.is_available():
        print("CUDA not available, using CPU")
        args.device = 'cpu'
    
    try:
        # 1. 设置配置
        config = setup_config(args.config, args.model, args.device)
        
        # 2. 加载模型并设置hooks
        model, hooks, feature_hooks = load_model_and_setup_hooks(config, args.model, args.device)
        
        # 3. 预处理图像
        batched_inputs, original_image = preprocess_image(args.image, config)
        
        # 4. 运行推理并捕获特征
        outputs, captured_features = run_inference_with_hooks(model, batched_inputs, feature_hooks, args.device)
        
        # 5. 可视化特征
        if captured_features:
            for feature_data in captured_features:
                visualize_conv_features(
                    feature_data['features'], 
                    feature_data['layer_name'], 
                    args.output,
                    args.max_channels
                )
            
            print(f"\n✓ Feature visualization completed!")
            print(f"Output directory: {args.output}")
            print(f"Visualized {len(captured_features)} layer(s)")
        else:
            print("\n✗ No features were captured. Check the hook registration.")
        
        # 6. 清理
        cleanup_hooks(hooks)
        
        return 0
        
    except Exception as e:
        print(f"Error during processing: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
