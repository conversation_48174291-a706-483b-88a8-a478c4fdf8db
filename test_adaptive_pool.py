#!/usr/bin/env python3
"""
测试自适应池化脚本
"""

import os
import cv2
import numpy as np
from pathlib import Path
import shutil

def create_test_images(output_dir="test_input_images"):
    """创建测试用的灰度图像"""
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    # 创建不同尺寸的测试图像
    test_images = [
        ("small_64x64.png", (64, 64)),
        ("medium_128x96.png", (128, 96)),
        ("large_256x192.png", (256, 192)),
        ("very_large_512x384.png", (512, 384)),
        ("square_100x100.jpg", (100, 100)),
        ("rectangle_200x50.bmp", (200, 50)),
    ]
    
    created_files = []
    
    for filename, (width, height) in test_images:
        # 创建带有图案的灰度图像
        image = np.zeros((height, width), dtype=np.uint8)
        
        # 添加一些图案
        # 渐变背景
        for y in range(height):
            for x in range(width):
                image[y, x] = int((x + y) * 255 / (width + height))
        
        # 添加矩形
        cv2.rectangle(image, (width//4, height//4), (3*width//4, 3*height//4), 128, -1)
        
        # 添加圆形
        center = (width//2, height//2)
        radius = min(width, height) // 6
        cv2.circle(image, center, radius, 255, -1)
        
        # 保存图像
        file_path = output_path / filename
        cv2.imwrite(str(file_path), image)
        created_files.append(file_path)
        print(f"Created: {filename} ({width}x{height})")
    
    return created_files, output_path

def test_single_image():
    """测试单张图像处理"""
    print("=== Testing Single Image Processing ===")
    
    # 创建测试图像
    test_files, input_dir = create_test_images()
    
    try:
        # 选择第一张图像进行测试
        test_image = test_files[0]
        output_image = "test_single_output.png"
        
        # 运行脚本
        import subprocess
        cmd = [
            "python", "adaptive_pool_grayscale.py",
            "--input", str(test_image),
            "--output", output_image,
            "--verbose"
        ]
        
        print(f"Running: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ Single image test passed!")
            print("Output:")
            print(result.stdout)
            
            # 检查输出文件
            if os.path.exists(output_image):
                # 读取并检查尺寸
                output_img = cv2.imread(output_image, cv2.IMREAD_GRAYSCALE)
                if output_img is not None and output_img.shape == (7, 7):
                    print(f"✓ Output image has correct size: {output_img.shape}")
                else:
                    print(f"✗ Output image has wrong size: {output_img.shape if output_img is not None else 'None'}")
                
                # 清理
                os.remove(output_image)
            else:
                print("✗ Output image not found")
        else:
            print("✗ Single image test failed!")
            print("Error:", result.stderr)
            
    except Exception as e:
        print(f"✗ Single image test failed with exception: {e}")
    finally:
        # 清理测试文件
        if input_dir.exists():
            shutil.rmtree(input_dir)

def test_batch_processing():
    """测试批量处理"""
    print("\n=== Testing Batch Processing ===")
    
    # 创建测试图像
    test_files, input_dir = create_test_images()
    output_dir = "test_batch_output"
    
    try:
        # 运行批量处理
        import subprocess
        cmd = [
            "python", "adaptive_pool_grayscale.py",
            "--input", str(input_dir),
            "--output", output_dir,
            "--verbose"
        ]
        
        print(f"Running: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ Batch processing test passed!")
            print("Output preview:")
            print(result.stdout[-500:])  # 显示最后500字符
            
            # 检查输出文件
            output_path = Path(output_dir)
            if output_path.exists():
                output_files = list(output_path.glob("*"))
                print(f"✓ Generated {len(output_files)} output files")
                
                # 检查每个输出文件的尺寸
                all_correct = True
                for output_file in output_files:
                    img = cv2.imread(str(output_file), cv2.IMREAD_GRAYSCALE)
                    if img is None or img.shape != (7, 7):
                        print(f"✗ {output_file.name} has wrong size: {img.shape if img is not None else 'None'}")
                        all_correct = False
                
                if all_correct:
                    print("✓ All output images have correct size (7x7)")
                
                # 清理
                shutil.rmtree(output_path)
            else:
                print("✗ Output directory not found")
        else:
            print("✗ Batch processing test failed!")
            print("Error:", result.stderr)
            
    except Exception as e:
        print(f"✗ Batch processing test failed with exception: {e}")
    finally:
        # 清理测试文件
        if input_dir.exists():
            shutil.rmtree(input_dir)

def test_direct_function_call():
    """测试直接函数调用"""
    print("\n=== Testing Direct Function Call ===")
    
    try:
        from adaptive_pool_grayscale import load_grayscale_image, adaptive_pool_to_7x7, save_pooled_image
        
        # 创建测试图像
        test_image = np.random.randint(0, 256, (100, 150), dtype=np.uint8)
        test_path = "direct_test_input.png"
        cv2.imwrite(test_path, test_image)
        
        # 测试加载
        tensor = load_grayscale_image(test_path)
        print(f"✓ Loaded image tensor shape: {tensor.shape}")
        
        # 测试池化
        pooled = adaptive_pool_to_7x7(tensor)
        print(f"✓ Pooled tensor shape: {pooled.shape}")
        
        # 测试保存
        output_path = "direct_test_output.png"
        save_pooled_image(pooled, output_path)
        
        # 验证结果
        result_img = cv2.imread(output_path, cv2.IMREAD_GRAYSCALE)
        if result_img is not None and result_img.shape == (7, 7):
            print(f"✓ Direct function test passed! Output shape: {result_img.shape}")
        else:
            print(f"✗ Direct function test failed! Output shape: {result_img.shape if result_img is not None else 'None'}")
        
        # 清理
        os.remove(test_path)
        os.remove(output_path)
        
    except Exception as e:
        print(f"✗ Direct function test failed: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("Adaptive Pool Grayscale Test Suite")
    print("=" * 50)
    
    # 检查依赖
    try:
        import torch
        import cv2
        import numpy as np
        from tqdm import tqdm
        print("✓ All dependencies available")
    except ImportError as e:
        print(f"✗ Missing dependency: {e}")
        return
    
    print(f"PyTorch version: {torch.__version__}")
    print(f"OpenCV version: {cv2.__version__}")
    print()
    
    # 运行测试
    test_direct_function_call()
    test_single_image()
    test_batch_processing()
    
    print("\n" + "=" * 50)
    print("Test suite completed!")
    print("\nUsage examples:")
    print("1. Single image:")
    print("   python adaptive_pool_grayscale.py --input image.png --output pooled.png")
    print("\n2. Batch processing:")
    print("   python adaptive_pool_grayscale.py --input ./input_dir --output ./output_dir")
    print("\n3. With specific extensions:")
    print("   python adaptive_pool_grayscale.py --input ./input --output ./output --extensions .png .jpg")

if __name__ == "__main__":
    main()
