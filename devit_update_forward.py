def forward(self, batched_inputs: List[Dict[str, torch.Tensor]]):
    # 获取批次大小 (batch size)
    bs = len(batched_inputs)
    # 初始化损失字典，用于存储训练时的各种损失
    loss_dict = {}
    # 在非训练模式下，断言批次大小为1 (通常推理时单张图片处理)
    if not self.training: assert bs == 1
    # 根据当前是训练模式还是测试模式，选择对应的类别原型权重
    # self.train_class_weight 包含基础类别的原型
    # self.test_class_weight 包含所有类别（基础类+新类）的原型
    class_weights = self.train_class_weight if self.training else self.test_class_weight
    # 获取当前模式下的类别总数（训练时为基础类别数，测试时为所有类别数）
    num_classes = len(class_weights)

    # --- 步骤 1: 离线RPN生成候选区域 (在 torch.no_grad() 环境下，不计算梯度) ---
    with torch.no_grad():
        # 确保离线骨干网络和RPN处于评估模式
        if self.offline_backbone.training or self.offline_proposal_generator.training:
            self.offline_backbone.eval()
            self.offline_proposal_generator.eval()
        # 1.1 对输入图像进行离线RPN所需的预处理（归一化、打包等）
        images_offline_rpn = self.offline_preprocess_image(batched_inputs)
        # 1.2 通过离线RPN的骨干网络提取特征
        features_offline_rpn = self.offline_backbone(images_offline_rpn.tensor)
        # 1.3 使用离线RPN生成候选区域 (proposals)
        # proposals 是一个列表，每个元素对应一张输入图片，包含其上的候选框
        proposals, _ = self.offline_proposal_generator(images_offline_rpn, features_offline_rpn, None)

        # 1.4 对输入图像进行主骨干网络所需的预处理
        images = self.preprocess_image(batched_inputs)
        # 获取预处理后图像张量的高度 H 和宽度 W
        H, W = images.tensor.shape[2:]

    # --- 步骤 2: 主骨干网络 (ViT) 提取特征 (同样在 torch.no_grad() 环境下，因为ViT是冻结的) ---
    with torch.no_grad():
        # 确保主骨干网络处于评估模式
        if self.backbone.training: self.backbone.eval()
        # 使用自动混合精度 (autocast) 进行前向传播，可能为了效率
        with autocast(enabled=True):
            # 从主骨干网络 (ViT) 获取所有层的 patch tokens
            all_patch_tokens = self.backbone(images.tensor)
        # 选择最后一层 (或排序后最深层) 的 patch tokens 作为主要特征
        output_key = sorted(list(all_patch_tokens.keys()), key=lambda x: int(x[3:]))[-1]
        patch_tokens = all_patch_tokens[output_key]  # 这是论文中的 h_vit

    # --- 步骤 3: 训练模式下的处理流程 ---
    if self.training:
        with torch.no_grad():  # 目标分配过程不计算梯度
            # 3.1 获取批次中每张图片的真实标注实例 (ground-truth instances)
            gt_instances = [x["instances"].to(self.device) for x in batched_inputs]
            # 提取真实边界框
            gt_boxes = [x.gt_boxes.tensor for x in gt_instances]
            # 提取RPN生成的提议框
            rpn_boxes = [x.proposal_boxes.tensor for x in proposals]

            # 3.2 为真实边界框添加噪声，生成 "noisy_boxes"，用于数据增强或辅助训练
            noisy_boxes = self.prepare_noisy_boxes(gt_boxes, images.tensor.shape)
            # 将真实框、噪声框、RPN提议框合并，作为匹配和采样的候选
            boxes_to_match = [torch.cat([gt_boxes[i], noisy_boxes[i], rpn_boxes[i]])
                              for i in range(len(batched_inputs))]

            # 初始化用于存储每个提议框匹配到的类别标签和真实框的列表
            class_labels_for_sampling = []
            matched_gt_boxes_for_sampling = []
            # 存储经过采样后的提议框
            resampled_proposals = []

            # (这些列表用于统计，不是核心逻辑的一部分)
            # num_bg_samples, num_fg_samples = [], []

            # 3.3 对每张图片的提议框进行匹配和采样
            for proposals_per_image, targets_per_image in zip(boxes_to_match, gt_instances):
                # 计算提议框与真实框之间的 IoU 矩阵
                match_quality_matrix = box_iou(
                    targets_per_image.gt_boxes.tensor, proposals_per_image
                )  # (N_gt, M_proposals)
                # 使用 self.proposal_matcher 进行匹配，得到每个提议框匹配到的真实框索引和匹配标签
                # matched_idxs: 每个提议框匹配到的真实框的索引 (若无匹配则为特定值)
                # matched_labels: 匹配标签 (1: 前景, 0: 背景, -1: 忽略)
                matched_idxs, matched_labels = self.proposal_matcher(match_quality_matrix)

                # 获取每个提议框对应的真实类别标签
                if len(targets_per_image.gt_classes) > 0:
                    class_labels_i = targets_per_image.gt_classes[matched_idxs]
                else:  # 如果图像中没有真实标注物体
                    assert torch.all(matched_labels == 0)  # 此时所有提议都应为背景
                    class_labels_i = torch.zeros_like(matched_idxs)

                # 将背景提议框的类别标签设置为 num_classes (一个特殊的背景ID)
                class_labels_i[matched_labels == 0] = num_classes
                # 将忽略提议框的类别标签设置为 -1
                class_labels_i[matched_labels == -1] = -1

                # 3.3.1 进行正负样本采样，以满足 RCNN 阶段的批大小和正负样本比例
                # (self.evaluation_shortcut 可能是用于某种快速评估的标志，这里主要看 self.training)
                if self.training:  # or self.evaluation_shortcut:
                    # 找出前景样本 (匹配到真实物体且不是忽略样本)
                    positive = ((class_labels_i != -1) & (class_labels_i != num_classes)).nonzero().flatten()
                    # 找出背景样本
                    negative = (class_labels_i == num_classes).nonzero().flatten()

                    # batch_size_per_image 是 RCNN 阶段每个图像处理的 ROI 数量 (例如 128 或 512)
                    # num_pos 是期望的正样本数量
                    num_pos = int(self.batch_size_per_image * self.pos_ratio)
                    # 确保不超过实际存在的可采样正样本数
                    num_pos = min(positive.numel(), num_pos)
                    # 计算期望的负样本数量
                    num_neg = self.batch_size_per_image - num_pos
                    # 确保不超过实际存在的可采样负样本数
                    num_neg = min(negative.numel(), num_neg)

                    # 随机打乱并选取指定数量的正负样本索引
                    perm1 = torch.randperm(positive.numel(), device=self.device)[:num_pos]
                    perm2 = torch.randperm(negative.numel())[:num_neg].to(self.device)
                    pos_idx = positive[perm1]
                    neg_idx = negative[perm2]
                    # 合并采样后的正负样本索引
                    sampled_idxs = torch.cat([pos_idx, neg_idx], dim=0)
                # else: # 如果不是训练或快速评估，则使用所有提议框 (通常不会走到这里，因为上面有 if self.training)
                #     sampled_idxs = torch.arange(len(proposals_per_image), device=self.device).long()

                # 根据采样后的索引，选取对应的提议框、类别标签和匹配到的真实框
                proposals_per_image = proposals_per_image[sampled_idxs]
                class_labels_i = class_labels_i[sampled_idxs]

                if len(targets_per_image.gt_boxes.tensor) > 0:
                    gt_boxes_i = targets_per_image.gt_boxes.tensor[matched_idxs[sampled_idxs]]
                else:  # 如果没有真实框，则用零向量填充 (后续背景样本的 gt_boxes 不会被用于回归损失)
                    gt_boxes_i = torch.zeros(len(sampled_idxs), 4, device=self.device)

                resampled_proposals.append(proposals_per_image)
                class_labels_for_sampling.append(class_labels_i)
                matched_gt_boxes_for_sampling.append(gt_boxes_i)

                # (统计代码，可以忽略)
                # num_bg_samples.append((class_labels_i == num_classes).sum().item())
                # num_fg_samples.append(class_labels_i.numel() - num_bg_samples[-1])

            # 将批次中所有图像的采样结果拼接起来
            class_labels = torch.cat(class_labels_for_sampling)  # 所有采样后提议框的类别标签
            matched_gt_boxes = torch.cat(matched_gt_boxes_for_sampling)  # 所有采样后提议框对应的真实框 (用于回归)

            # 准备 ROI Align 的输入 (rois)
            # rois 的格式是 [batch_idx, x1, y1, x2, y2]
            rois = []
            for bid, box_list_per_image in enumerate(resampled_proposals):
                batch_index = torch.full((len(box_list_per_image), 1), fill_value=float(bid)).to(self.device)
                rois.append(torch.cat([batch_index, box_list_per_image], dim=1))
            rois = torch.cat(rois)  # (N_total_rois, 5)
    else:  # --- 步骤 3': 测试模式下的处理流程 (准备提议框) ---
        # 直接使用 RPN 生成的所有提议框 (只处理批次中的第一张图，因为推理时 bs=1)
        boxes = proposals[0].proposal_boxes.tensor
        # 准备 ROI Align 的输入，批次索引为0
        rois = torch.cat([torch.full((len(boxes), 1), fill_value=0).to(self.device),
                          boxes], dim=1)

    # --- 步骤 4: 提议区域增强和真值区域准备 (训练时) / 提议区域增强 (测试时) ---
    if self.training:
        # 筛选出前景提议框 (匹配到真实物体的) 和背景提议框
        fg_indices = class_labels != num_classes
        bg_indices = class_labels == num_classes
        # 对于背景提议框，其“真值框”就是其自身 (不参与回归损失)
        # (这行代码的目的是为 augment_rois 提供一个 matched_gt_boxes，对于背景区域，这个GT就是其本身)
        # (实际上 augment_rois 对背景区域可能不会做特别的增强或不需要精确的GT进行指导)
        # 注意：这里的 matched_gt_boxes 是采样后的，class_labels 也是采样后的
        # rois 也是采样后的，所以它们的长度应该是一致的
        # 这行代码的意图可能是确保 matched_gt_boxes 对于背景样本有合理的坐标，
        # 但 augment_rois 内部对背景的处理可能不同。
        # 一个更清晰的做法可能是只对前景调用 augment_rois 时提供精确的 matched_gt_boxes
        # 或者 augment_rois 内部会区分前景背景。
        # 假设 augment_rois 需要所有 rois 都有一个对应的 gt_box (即使是背景，用其自身)
        # matched_gt_boxes_for_aug = rois[:, 1:].clone() # 创建一个副本
        # matched_gt_boxes_for_aug[fg_indices] = matched_gt_boxes[fg_indices] # 用真实匹配的GT覆盖前景部分
        # 实际上，论文的方法是为前景区域生成 gt_region 和 gt_region_coords
        # 这里的 matched_gt_boxes[bg_indices] = rois[bg_indices, 1:]
        # 确保了背景区域在 augment_rois 中有 "参考框"，但它们不会被用来计算精确的 gt_region_coords
        # 这里的 matched_gt_boxes 是采样后的，长度与 rois 相同
        temp_matched_gt_boxes = rois[:, 1:].clone()  # 先用ROI自身作为“GT”
        temp_matched_gt_boxes[fg_indices] = matched_gt_boxes[fg_indices]  # 对于前景，使用真实的GT

        # 调用 augment_rois 函数：
        # 1. 扩展提议框 (aug_rois)
        # 2. 生成初始的传播区域 (init_region, 通常是扩展框中心的一个固定大小区域)
        # 3. 生成训练时用于监督掩码学习的真实区域掩码 (gt_region)
        # 4. (这里省略了返回值)
        # 注意：这里 matched_gt_boxes 应该是经过筛选的，只包含前景的
        # 但 augment_rois 可能是对所有 rois 操作，然后根据 fg_indices 来取用 gt_region
        # 代码中 gt_region 和 gt_region_coords 的获取是在 augment_rois 之后，并用 fg_indices 筛选
        aug_rois, init_region, gt_region_for_all, _ = augment_rois(
            rois[:, 1:],  # 传入所有采样后的提议框 (去除批次索引)
            temp_matched_gt_boxes,  # 传入对应的“真实框”(背景为其自身，前景为匹配的GT)
            img_h=H, img_w=W, pooler_size=self.roialign_size,
            min_expansion=0.4, expand_shortest=True
        )
        aug_rois = torch.cat([rois[:, :1], aug_rois], dim=1)  # 将批次索引重新拼接回去
        # 将真实边界框坐标转换为相对于 aug_rois 的区域内坐标，用于监督边界框回归
        gt_region_coords = abs_coord_2_region_coord(aug_rois[fg_indices, 1:], matched_gt_boxes[fg_indices],
                                                    self.roialign_size)
        # 从 augment_rois 的输出中筛选出前景对应的真实区域掩码
        gt_region = gt_region_for_all[fg_indices].float().flatten(1)  # 并展平
    else:  # 测试模式
        # 只进行提议框增强和初始区域生成，不需要真实掩码和坐标
        aug_rois, init_region, _, _ = augment_rois(
            rois[:, 1:], None, img_h=H, img_w=W, pooler_size=self.roialign_size,
            min_expansion=0.4, expand_shortest=True
        )
        aug_rois = torch.cat([rois[:, :1], aug_rois], dim=1)

    # --- 步骤 5: 从 ViT 特征图中提取 ROI 特征 ---
    # 5.1 提取 7x7 的 ROI 特征，可能用于初始的类别打分 (得到 init_scores)
    roi_features_origin = self.roi_align_77(patch_tokens, rois)  # 输入: ViT特征, 原始提议框 (N_rois, C, 7, 7)
    # 5.2 提取 KxK (self.roialign_size x self.roialign_size) 的 ROI 特征，用于后续的特征子空间投影和区域传播
    roi_features = self.roi_align(patch_tokens, aug_rois)  # 输入: ViT特征, 增强/扩展后的提议框 (N_rois, C, K, K)

    roi_bs = len(roi_features)  # 获取 ROI 的数量

    # 将 ROI 特征展平 (空间维度合并到通道维度之后)
    roi_features_origin = roi_features_origin.flatten(2)  # (N_rois, C, 7*7)
    roi_features = roi_features.flatten(2)  # (N_rois, C, K*K)

    # 获取展平后的空间维度大小
    # bs_roi_feat, spatial_size_roi_feat = roi_features.shape[0], roi_features.shape[-1] # 原作者变量名，易混淆
    num_rois_total, spatial_dim_flat = roi_features.shape[0], roi_features.shape[-1]

    # --- 步骤 6: 特征子空间投影的实现 ---
    # 6.1 计算 ROI 特征与所有类别原型 (class_weights) 的点积
    # roi_features: (N_rois, C_vit, K*K) -> transpose: (N_rois, K*K, C_vit)
    # class_weights: (N_classes, C_vit) -> transpose: (C_vit, N_classes)
    # feats: (N_rois, K*K, N_classes) -> 代表每个ROI在每个空间位置上与每个类别的相似度
    feats = roi_features.transpose(-2, -1) @ class_weights.T

    # 6.2 根据初始打分选择 Top-T 个类别 (num_active_classes 即 T 值)
    num_active_classes = self.num_sample_class  # 即配置文件中的 DE.TOPK
    # sample_class_enabled = True # 这个标志似乎没有被使用

    # 使用 7x7 ROI 特征的平均值与类别原型点积，得到初始的类别分数
    init_scores = F.normalize(roi_features_origin.mean(dim=2), dim=1) @ class_weights.T  # (N_rois, N_classes)
    # 选取每个 ROI 得分最高的 T 个类别的索引
    topk_class_indices = torch.topk(init_scores, num_active_classes, dim=1).indices  # (N_rois, T)

    # 6.3 (训练时) 确保真实类别包含在 Top-T 类别中，以稳定训练
    if self.training:
        class_indices_for_projection = []
        for i in range(roi_bs):  # roi_bs 就是 N_rois_total
            curr_gt_label = class_labels[i].item()  # 当前 ROI 对应的真实类别标签 (或背景标签 num_classes)
            topk_indices_for_roi_i = topk_class_indices[i].cpu()  # 当前 ROI 的 Top-T 类别索引

            # 如果真实类别已经是 Top-T 之一，或者真实类别是背景，则直接使用 RPN预测的 Top-T
            if curr_gt_label in topk_indices_for_roi_i or curr_gt_label == num_classes:
                curr_selected_indices = topk_indices_for_roi_i
            else:  # 如果真实类别不在 Top-T 中，则用真实类别替换掉 Top-T 中的最后一个
                curr_selected_indices = torch.cat([torch.as_tensor([curr_gt_label]),
                                                   topk_indices_for_roi_i[:-1]])
            class_indices_for_projection.append(curr_selected_indices)
        class_indices = torch.stack(class_indices_for_projection).to(self.device)  # (N_rois, T)
    else:  # 测试时直接使用 Top-T 类别
        class_indices = topk_class_indices

    # 对选择出的 T 个类别索引进行排序 (确保后续处理顺序一致)
    class_indices = torch.sort(class_indices, dim=1).values  # (N_rois, T)

    # 6.4 构建特征子空间投影的各个组成部分 (对应论文 Eq.5)
    # feats: (N_rois, K*K, N_classes)
    # class_indices: (N_rois, T)

    # 6.4.1 处理 "其他类别" (p_{C\c}·h_vit 部分, 对应 inter_dist_emb)
    other_classes_features_list = []
    # 创建一个索引张量，方便后面使用 torch.gather
    # indexes_all_classes: (1, 1, N_classes) -> repeat: (N_rois, K*K, N_classes)
    indexes_all_classes = torch.arange(0, num_classes, device=self.device)[None, None, :].repeat(num_rois_total,
                                                                                                 spatial_dim_flat, 1)

    # 对选出的 T 个类别中的每一个类别 c (由 class_indices[:, i] 指定)
    # 收集其对应的 "其他类别" (C\c) 的相似度特征
    for i in range(num_active_classes):  # 遍历 Top-T 中的每个选定类别
        # current_target_class_indices: (N_rois, 1, 1)
        current_target_class_indices = class_indices[:, i].view(-1, 1, 1)
        # cmask: (N_rois, K*K, N_classes), bool 张量, 标记哪些不是当前目标类别 c
        cmask = indexes_all_classes != current_target_class_indices
        # gathered_other_class_feats: (N_rois, K*K, N_classes - 1)
        # 从 feats 中提取出所有非 c 类别的相似度特征
        gathered_other_class_feats = torch.gather(feats, 2,
                                                  indexes_all_classes[cmask].view(num_rois_total, spatial_dim_flat,
                                                                                  num_classes - 1))
        other_classes_features_list.append(gathered_other_class_feats[:, :, None, :])  # (N_rois, K*K, 1, N_classes - 1)

    # other_classes_batched: (N_rois, K*K, T, N_classes - 1)
    other_classes_batched = torch.cat(other_classes_features_list, dim=2)
    # permute & flatten: (N_rois * T, K*K, N_classes - 1)
    # 这样每个 (roi, target_class_from_top_t) 组合都有了其对应的 (N_classes - 1) 个其他类别的特征
    other_classes_batched = other_classes_batched.permute(0, 2, 1, 3).flatten(0, 1)

    # 对每个 (roi, target_class_from_top_t) 的 "其他类别" 特征进行排序 (channel-reorder的第一步)
    other_classes_batched, _ = torch.sort(other_classes_batched, dim=-1)  # 排序默认是升序
    # 进行插值 (channel-reorder的第二步), 插值到 self.T (配置文件中的 DE.T, 例如128)
    # 输入: (N_rois*T, K*K, N_classes-1), 输出: (N_rois*T, K*K, self.T)
    # 注意: interpolate 函数的输入期望是 BxCxL, 这里 K*K 是 L, N_classes-1 是 C
    # 需要调整维度顺序或 interpolate 函数内部处理
    # 假设 interpolate 函数能处理 (..., C_in) -> (..., C_out)
    # 或者更可能是 (B, C, L_in) -> (B, C, L_out)
    # 从代码看 interpolate(other_classes, self.T, mode='linear')， T是目标长度 L_out
    # other_classes (N_rois*T, K*K, N_classes-1) -> permute (N_rois*T, N_classes-1, K*K)
    # 输出 (N_rois*T, N_classes-1, self.T) ? 还是 (N_rois*T, self.T, K*K) ?
    # 论文中 channel-reorder 是对通道排序和插值。这里的 K*K 更像空间维度 L。
    # N_classes-1 是通道数 C。目标是插值通道到 self.T。
    # 所以应该是 other_classes_batched.permute(0,2,1) -> (N_rois*T, N_classes-1, K*K)
    # 然后 interpolate(other_classes_batched.permute(0,2,1), self.T) -> (N_rois*T, self.T, K*K)
    # 但后续的线性层 self.foreground_linears['other_classes'](other_classes) 输入是 self.T
    # 输出是 dist_emb_size。这表明 other_classes 经过 interpolate 后应该是 (..., self.T)
    # 所以 interpolate 作用在最后一个维度，将其从 N_classes-1 插值到 self.T
    other_classes_interpolated = interpolate(other_classes_batched, self.T, mode='linear')  # (N_rois*T, K*K, self.T)

    # 通过线性层进行嵌入
    # 输入 (N_rois*T, K*K, self.T), 线性层权重 (self.T, dist_emb_size)
    # 输出 (N_rois*T, K*K, dist_emb_size)
    inter_dist_emb_flat = self.foreground_linears['other_classes'](other_classes_interpolated)
    # 转换维度以匹配后续拼接: (N_rois*T, dist_emb_size, K*K)
    inter_dist_emb_flat = inter_dist_emb_flat.permute(0, 2, 1)
    # Reshape 成网络期望的4D张量: (N_rois*T, dist_emb_size, self.roialign_size, self.roialign_size)
    inter_dist_emb = inter_dist_emb_flat.reshape(num_rois_total * num_active_classes, -1, self.roialign_size,
                                                 self.roialign_size)

    # 6.4.2 处理 "当前类别" (p_c·h_vit 部分, 对应 intra_dist_emb)
    # intra_feats: (N_rois, K*K, T), 提取出每个ROI对应其Top-T中每个类别的相似度特征
    intra_feats = torch.gather(feats, 2, class_indices[:, None, :].repeat(1, spatial_dim_flat, 1))
    # intra_feats_flat: (N_rois*K*K, T)
    # distance_embed 对每个空间位置的T个类别相似度值进行嵌入
    # 输出: (N_rois*K*K, T, pos_emb_size)
    intra_dist_emb_spatial_flat = distance_embed(intra_feats.flatten(0, 1), num_pos_feats=self.pos_emb_size)
    # 通过线性层进行嵌入
    # 输入 (N_rois*K*K, T, pos_emb_size), 线性层权重 (pos_emb_size, dist_emb_size)
    # 输出 (N_rois*K*K, T, dist_emb_size)
    intra_dist_emb_spatial_flat = self.foreground_linears['current_class'](intra_dist_emb_spatial_flat)
    # Reshape: (N_rois, K*K, T, dist_emb_size)
    intra_dist_emb_spatial = intra_dist_emb_spatial_flat.reshape(num_rois_total, spatial_dim_flat, num_active_classes,
                                                                 -1)
    # 转换维度以匹配后续拼接: (N_rois, T, dist_emb_size, K*K) -> flatten (N_rois*T, dist_emb_size, K*K)
    intra_dist_emb_flat = intra_dist_emb_spatial.permute(0, 2, 3, 1).flatten(0, 1)
    # Reshape 成网络期望的4D张量: (N_rois*T, dist_emb_size, self.roialign_size, self.roialign_size)
    intra_dist_emb = intra_dist_emb_flat.reshape(num_rois_total * num_active_classes, -1,
                                                 self.roialign_size, self.roialign_size)

    # 6.4.3 处理 "背景类别" (p_B·h_vit 部分, 对应 bg_dist_emb_c)
    # roi_features: (N_rois, C_vit, K*K) -> transpose: (N_rois, K*K, C_vit)
    # self.bg_tokens: (N_bg_tokens, C_vit) -> transpose: (C_vit, N_bg_tokens)
    # bg_feats: (N_rois, K*K, N_bg_tokens)
    bg_feats = roi_features.transpose(-2, -1) @ self.bg_tokens.T
    # 通过线性层进行嵌入
    # 输入 (N_rois, K*K, N_bg_tokens), 线性层权重 (N_bg_tokens, pos_emb_size)
    # 输出 (N_rois, K*K, pos_emb_size)
    bg_dist_emb_spatial = self.foreground_linears['background'](bg_feats)
    # 转换维度: (N_rois, pos_emb_size, K*K)
    bg_dist_emb_spatial = bg_dist_emb_spatial.permute(0, 2, 1)
    # Reshape 成4D张量: (N_rois, pos_emb_size, self.roialign_size, self.roialign_size)
    bg_dist_emb_reshaped = bg_dist_emb_spatial.reshape(num_rois_total, -1, self.roialign_size, self.roialign_size)
    # 为每个选定的 Top-T 类别复制一份背景嵌入
    # bg_dist_emb_c: (N_rois*T, pos_emb_size, self.roialign_size, self.roialign_size)
    bg_dist_emb_c = bg_dist_emb_reshaped[:, None, :, :, :].expand(-1, num_active_classes, -1, -1, -1).flatten(0, 1)

    # 6.4.4 处理原始 ROI 特征 (h_vit 部分，经过投影)
    # roi_features: (N_rois, C_vit, K*K) -> transpose: (N_rois, K*K, C_vit)
    # 线性层权重 (input_feat_dim/C_vit, proj_feat_dim)
    # 输出: (N_rois, K*K, proj_feat_dim)
    projected_feats_spatial = self.foreground_linears['feat'](roi_features.transpose(-2, -1))
    # 转换维度: (N_rois, proj_feat_dim, K*K)
    projected_feats_spatial = projected_feats_spatial.permute(0, 2, 1)
    # Reshape 成4D张量: (N_rois, proj_feat_dim, self.roialign_size, self.roialign_size)
    projected_feats_reshaped = projected_feats_spatial.reshape(num_rois_total, -1, self.roialign_size,
                                                               self.roialign_size)
    # 为每个选定的 Top-T 类别复制一份投影后的 ROI 特征
    # projected_feats_c: (N_rois*T, proj_feat_dim, self.roialign_size, self.roialign_size)
    projected_feats_c = projected_feats_reshaped[:, None, :, :, :].expand(-1, num_active_classes, -1, -1, -1).flatten(0,
                                                                                                                      1)

    # 6.5 拼接构成前景路径的输入 fg_x (即 h_subspace,c 的 batched 版本)
    # 顺序对应论文 Eq.5: p_c·h_vit (intra_dist_emb), channel-reorder(p_{C\c}·h_vit) (inter_dist_emb),
    # p_B·h_vit (bg_dist_emb_c), 和一个额外投影的 h_vit (projected_feats_c)
    # 注意维度：intra_dist_emb 和 inter_dist_emb 的通道数是 dist_emb_size
    # bg_dist_emb_c 的通道数是 pos_emb_size
    # projected_feats_c 的通道数是 proj_feat_dim
    # rpropnet 的输入通道数是 proj_feat_dim + 2*dist_emb_size + pos_emb_size，这里拼接顺序可能与声明的维度顺序有关
    fg_x = torch.cat([intra_dist_emb, inter_dist_emb, bg_dist_emb_c, projected_feats_c], dim=1)

    # 6.6 准备背景路径的输入 bg_x
    # 对所有类别的相似度特征进行排序和插值
    # feats: (N_rois, K*K, N_classes) -> sort: (N_rois, K*K, N_classes)
    # interpolate: (N_rois, K*K, self.T)
    cls_dist_feats_for_bg = interpolate(torch.sort(feats, dim=2).values, self.T, mode='linear')
    # 通过线性层嵌入
    # 输入 (N_rois, K*K, self.T), 线性层权重 (self.T, dist_emb_size)
    # 输出 (N_rois, K*K, dist_emb_size)
    bg_cls_dist_emb_spatial = self.background_linears['classes'](cls_dist_feats_for_bg)
    # 转换维度和 Reshape: (N_rois, dist_emb_size, self.roialign_size, self.roialign_size)
    bg_cls_dist_emb_reshaped = bg_cls_dist_emb_spatial.permute(0, 2, 1).reshape(num_rois_total, -1, self.roialign_size,
                                                                                self.roialign_size)

    # 背景原型的嵌入 (与 fg_x 中的 bg_dist_emb_reshaped 类似，但使用 self.background_linears)
    bg_dist_emb_for_bg_spatial = self.background_linears['background'](bg_feats)
    bg_dist_emb_for_bg_reshaped = bg_dist_emb_for_bg_spatial.permute(0, 2, 1).reshape(num_rois_total, -1,
                                                                                      self.roialign_size,
                                                                                      self.roialign_size)

    # 原始 ROI 特征的投影 (与 fg_x 中的 projected_feats_reshaped 类似，但使用 self.background_linears)
    projected_feats_for_bg_spatial = self.background_linears['feat'](roi_features.transpose(-2, -1))
    projected_feats_for_bg_reshaped = projected_feats_for_bg_spatial.permute(0, 2, 1).reshape(num_rois_total, -1,
                                                                                              self.roialign_size,
                                                                                              self.roialign_size)

    # 拼接构成背景路径的输入 bg_x
    # rpropnet_bg 的输入通道数是 proj_feat_dim + dist_emb_size + pos_emb_size
    bg_x = torch.cat([bg_cls_dist_emb_reshaped, bg_dist_emb_for_bg_reshaped, projected_feats_for_bg_reshaped], dim=1)

    # --- 步骤 7: 通过区域传播网络 (Region Propagation Network) ---
    # fg_x: (N_rois*T, C_fg, K, K)
    # init_region: (N_rois, 1, K, K) -> repeat for T classes: (N_rois*T, 1, K, K)
    # fg_output 是一个列表，包含多个传播层 (PL) 的输出字典
    fg_output = self.rpropnet(fg_x, init_region[:, None, :, :].repeat(1, num_active_classes, 1, 1).flatten(0, 1))
    # bg_x: (N_rois, C_bg, K, K)
    # init_region: (N_rois, 1, K, K) -> unsqueeze for "class" dim: (N_rois, 1, 1, K, K) then select [:,0] ?
    #  rpropnet_bg 输入的 init_region 应该是 (N_rois, 1, K, K)
    # 这里 init_region[:, None, :, :] -> (N_rois, 1, 1, K, K), 似乎是为了匹配 batch 维度然后去掉
    # 如果 rpropnet_bg 也期望批次化的 init_region，则应该直接传递 init_region
    # 或者如果它处理单个 item，则需要调整。从参数 classification_only=True 推断，它可能结构简单。
    # 假设 init_region 维度应为 (N_rois, 1, K, K)
    # fg_output[0]['class_score'].reshape(bs, num_active_classes) -> (N_rois, num_active_classes) if bs (batch_size_per_image for rois) is N_rois
    # 这里的 bs 应该是图像的批次大小 (len(batched_inputs))
    # 而 N_rois_total 是所有图像中 ROI 的总数。
    # reshape(bs, num_active_classes, ...) 意味着输出是按图像批次组织的。
    # 因此，fg_output 的结果需要能够被 reshape 成 (N_rois_total / T_per_roi, T_per_roi, ...)
    # 或者更可能是 (bs_img, N_rois_per_img * T, ...) -> reshape (bs_img * N_rois_per_img, T, ...)
    # 这里的 `fg_output[-1]['class_score'].reshape(bs, num_active_classes)` 中的 `bs` 应指 `num_rois_total`
    # 如果每个ROI都选了 `num_active_classes`，那么 `fg_output` 的第一维是 `num_rois_total * num_active_classes`
    # 那么 `reshape(num_rois_total, num_active_classes)` 才对。
    # 这里的 `bs` (len(batched_inputs)) 如果大于1，且每个图像的ROI数量不同，直接 reshape(bs, ...) 会有问题。
    # 假设 `bs` 在这里指 `num_rois_total` (即所有ROI的数量，每个ROI都为其选定的T个类产生一个输出)
    # 但后续 `pred_region_coords.reshape(bs, num_active_classes, 4)` 也是用的这个`bs`
    # 这暗示了代码实现时，可能对每个图像的ROI分别处理，或者这里的`bs`有特定含义。
    # Detectron2通常将一个图像批次的所有ROI合并处理，所以 `num_rois_total` 更合理。
    # 我们暂时按 `num_rois_total` 理解，如果后续损失计算按图像批次，则需调整。
    # 从 `rois = torch.cat(rois)`来看，所有图像的ROI已合并。

    # bg_output 也是一个列表，包含多个传播层的输出
    bg_output = self.rpropnet_bg(bg_x, init_region)  # 这里的 init_region 是 (N_rois, 1, K, K)

    # --- 步骤 8: 训练模式下的损失计算 ---
    if self.training:
        # 用于计算掩码损失的真实掩码 gt_region 已经准备好 (只针对前景)
        # gt_region: (N_fg_rois, K*K)
        # 用于计算边界框回归损失的真实坐标 gt_region_coords 也已准备好 (只针对前景)
        # gt_region_coords: (N_fg_rois, 4)

        # 计算前景掩码的总数，用于归一化损失
        num_masks = fg_indices.sum()  # fg_indices 是基于采样后 class_labels 计算的

        # 获取前景ROI的原始索引 (在采样后的ROI列表中的索引)
        fg_indices_int = fg_indices.nonzero().flatten()
        # 对于每个前景ROI，找出其真实类别在它自己选择的Top-T类别中的索引
        # class_labels[fg_indices] 包含的是真实类别ID (0 到 num_classes-1)
        # class_indices[fg_indices] 包含的是每个前景ROI选择的Top-T类别ID (排序后的)
        # (class_labels[fg_indices][:, None] == class_indices[fg_indices]) 会产生一个布尔矩阵
        # .nonzero()[:, 1] 取出匹配成功的那个Top-T类别在其Top-T列表中的索引 (0 到 T-1)
        # fg_class_indices_in_top_t: (N_fg_rois)
        fg_class_indices_in_top_t = (class_labels[fg_indices][:, None] == class_indices[fg_indices_int]).nonzero()[:, 1]

        # 存储每一层预测的分类 logits
        logits_all_layers = []
        # 遍历每个传播层 (PL) 的输出
        for layer_id, (fgo, bgo) in enumerate(zip(fg_output, bg_output)):
            # fgo['class_score']: (N_rois*T, 1) -> reshape: (N_rois, T) (这里假设 bs 是 N_rois)
            # bgo['class_score']: (N_rois, 1)
            # c_scores_per_roi: (N_rois_total, num_active_classes)
            c_scores_per_roi = fgo['class_score'].reshape(num_rois_total, num_active_classes)
            # bg_score_per_roi: (N_rois_total, 1)
            bg_score_per_roi = bgo['class_score']
            # 将前景T个类别的分数和背景分数拼接，并应用温度系数
            # logits_per_layer: (N_rois_total, T + 1)
            logits_all_layers.append(torch.cat([c_scores_per_roi, bg_score_per_roi], dim=1) / self.cls_temp)

            # 提取前景ROI对应其真实类别的预测框坐标和掩码 logits
            # fgo['box_coords']: (N_rois*T, 4) -> reshape: (N_rois, T, 4)
            # fgo['output_region']: (N_rois*T, 1, K, K) -> reshape: (N_rois, T, K*K) (假设已展平)
            # pred_box_coords_all_top_t: (N_rois_total, num_active_classes, 4)
            pred_box_coords_all_top_t = fgo['box_coords'].reshape(num_rois_total, num_active_classes, 4)
            # pred_mask_logits_all_top_t: (N_rois_total, num_active_classes, K*K)
            pred_mask_logits_all_top_t = fgo['output_region'].reshape(num_rois_total, num_active_classes,
                                                                      -1)  # K*K = spatial_dim_flat? no, self.roialign_size**2

            # 根据 fg_indices_int (前景ROI的索引) 和 fg_class_indices_in_top_t (真实类别在Top-T中的索引)
            # 提取出真正用于监督的预测框和掩码
            # pred_box_coords_for_gt_class: (N_fg_rois, 4)
            pred_box_coords_for_gt_class = pred_box_coords_all_top_t[fg_indices_int, fg_class_indices_in_top_t]
            # pred_mask_logits_for_gt_class: (N_fg_rois, K*K)
            pred_mask_logits_for_gt_class = pred_mask_logits_all_top_t[fg_indices_int, fg_class_indices_in_top_t]

            # 计算掩码损失 (BCE Loss 和 Dice Loss)
            if num_masks > 0:  # 只有当存在前景掩码时才计算
                loss_dict[f"region_bce_loss_{layer_id}"] = sigmoid_ce_loss(pred_mask_logits_for_gt_class, gt_region,
                                                                           num_masks)
                loss_dict[f"region_dice_loss_{layer_id}"] = dice_loss(pred_mask_logits_for_gt_class, gt_region,
                                                                      num_masks)
                # 计算边界框坐标的 L1 损失和 GIoU 损失 (针对区域内相对坐标)
                loss_dict[f'rg_l1_loss_{layer_id}'] = F.l1_loss(pred_box_coords_for_gt_class, gt_region_coords)
                try:  # GIoU 计算可能因无效框而出错，用 try-except 包裹
                    loss_dict[f'rg_giou_loss_{layer_id}'] = (1 - torch.diag(generalized_box_iou(
                        box_cxcywh_to_xyxy(pred_box_coords_for_gt_class),  # 预测坐标转为 xyxy
                        box_cxcywh_to_xyxy(gt_region_coords)))).mean()  # 真值坐标转为 xyxy
                except:  # 如果 GIoU 计算失败，则不记录此损失或记录为0
                    loss_dict[f'rg_giou_loss_{layer_id}'] = torch.zeros(1, device=self.device).sum() * 0.0
            else:  # 如果没有前景掩码，则损失设为0
                loss_dict[f"region_bce_loss_{layer_id}"] = pred_mask_logits_for_gt_class.sum() * 0.0
                loss_dict[f"region_dice_loss_{layer_id}"] = pred_mask_logits_for_gt_class.sum() * 0.0
                loss_dict[f'rg_l1_loss_{layer_id}'] = pred_box_coords_for_gt_class.sum() * 0.0
                loss_dict[f'rg_giou_loss_{layer_id}'] = pred_box_coords_for_gt_class.sum() * 0.0


    else:  # --- 步骤 8': 测试模式下的 logits 准备 ---
        # 直接取最后一层传播层 (PL) 的输出作为最终的分类 logits
        # cls_logits_fg: (N_rois_total, num_active_classes)
        cls_logits_fg = fg_output[-1]['class_score'].reshape(num_rois_total, num_active_classes)
        # bg_logits_final: (N_rois_total, 1)
        bg_logits_final = bg_output[-1]['class_score']
        # 拼接前景T个类别分数和背景分数，并应用温度系数
        # logits: (N_rois_total, T + 1)
        logits = torch.cat([cls_logits_fg, bg_logits_final], dim=1) / self.cls_temp

    # --- 步骤 9: 边界框回归 (训练和测试都需要，但处理方式不同) ---
    # 获取最后一层 PL 输出的预测的区域内相对坐标
    # pred_region_coords_final: (N_rois*T, 4)
    pred_region_coords_final = fg_output[-1]['box_coords']
    # 将这些区域内相对坐标转换回相对于原始图像的绝对坐标
    # aug_rois: (N_rois, 5) -> repeat for T classes: (N_rois, T, 5) -> flatten: (N_rois*T, 5)
    # (这里 aug_rois 的重复方式需要与 pred_region_coords_final 对应)
    # aug_rois_for_box_pred: (N_rois_total * num_active_classes, 5)
    # 假设 aug_rois 已准备好，每个ROI对应T个增强框 (或每个增强框用于T个类别)
    # 这里的 aug_rois 应该是 (N_rois_total, 5) 的形状，然后按需扩展
    # 从代码看，aug_rois (N_rois, 5) 是针对每个ROI的单个增强框
    # 而 pred_region_coords_final (N_rois*T, 4) 是每个ROI为其TopT中的每个类都预测了一个框
    # 所以 aug_rois 需要扩展以匹配
    # aug_rois_expanded: (N_rois_total * num_active_classes, 5)
    aug_rois_expanded = aug_rois[:, None, :].repeat(1, num_active_classes, 1).flatten(0, 1)
    # pred_abs_boxes: (N_rois*T, 4)
    pred_abs_boxes = region_coord_2_abs_coord(aug_rois_expanded[:, 1:], pred_region_coords_final, self.roialign_size)

    if self.training:
        # 对于训练，我们只关心前景ROI对应其真实类别的那个预测框
        # matched_gt_boxes (采样并筛选前景后): (N_fg_rois, 4)
        # fg_proposals (原始提议框，采样并筛选前景后): (N_fg_rois, 4)
        fg_proposals = rois[fg_indices, 1:]
        # pred_abs_boxes 需要被 reshape 并从中提取对应真实类别的预测
        # pred_abs_boxes_reshaped: (N_rois, T, 4)
        pred_abs_boxes_reshaped = pred_abs_boxes.reshape(num_rois_total, num_active_classes, 4)
        # pred_abs_boxes_for_gt_class: (N_fg_rois, 4)
        pred_abs_boxes_for_gt_class = pred_abs_boxes_reshaped[fg_indices_int, fg_class_indices_in_top_t]

        # 计算预测框相对于原始提议框的偏移量 (deltas)，用于监督
        fg_pred_deltas = self.box2box_transform.get_deltas(fg_proposals, pred_abs_boxes_for_gt_class)
    else:  # 测试模式
        # 计算所有 (ROI, Top-T class) 组合的预测框相对于原始提议框的偏移量
        # rois: (N_rois, 5) -> repeat: (N_rois, T, 4) -> flatten: (N_rois*T, 4)
        # (注意这里 rois[:, 1:] 的维度)
        pred_deltas = self.box2box_transform.get_deltas(
            rois[:, None, 1:].repeat(1, num_active_classes, 1).flatten(0, 1),  # (N_rois*T, 4)
            pred_abs_boxes  # (N_rois*T, 4)
        )
        # pred_deltas: (N_rois*T, 4) -> reshape: (N_rois, T*4)
        pred_deltas = pred_deltas.reshape(num_rois_total, num_active_classes * 4)

    # --- 步骤 10: 训练模式下的最终损失计算和返回 ---
    if self.training:
        # 准备分类损失的目标标签 (class_labels)
        # class_labels 当前是真实类别ID (0 到 num_classes-1，背景是 num_classes)
        # 需要将其转换为相对于 Top-T 选中类别 (+背景类别) 的索引 (0 到 T)
        # fg_indices, bg_indices 是基于采样后的 class_labels
        # class_labels[fg_indices] 包含了前景ROI的真实类别ID
        # class_indices[fg_indices] 包含了这些前景ROI各自选中的Top-T类别ID
        # (class_indices == class_labels.view(-1, 1)) 应该写作 (class_indices[fg_indices] == class_labels[fg_indices].view(-1,1))
        # .nonzero()[:,1] 给出真实类别在Top-T中的索引
        class_labels_for_loss = class_labels.clone().long()  # 使用 long 类型
        class_labels_for_loss[fg_indices] = fg_class_indices_in_top_t  # 前景标签设为其在Top-T中的索引
        class_labels_for_loss[bg_indices] = num_active_classes  # 背景标签设为 T (即第 T+1 个输出，对应背景logit)

        # 记录分类相关的统计信息 (准确率等)
        _log_classification_stats(logits_all_layers[-1].detach(), class_labels_for_loss)

        # 计算每个传播层的分类损失 (Focal Loss)
        for i, l_logits in enumerate(logits_all_layers):
            loss = focal_loss(l_logits, class_labels_for_loss, num_classes=num_active_classes,
                              bg_weight=self.bg_cls_weight)
            loss_dict[f'focal_loss_{i}'] = loss

        # 计算边界框回归损失 (Smooth L1 Loss)
        # gt_pred_deltas 是真实框相对于原始提议框的偏移量
        gt_pred_deltas = self.box2box_transform.get_deltas(
            fg_proposals,  # (N_fg_rois, 4)
            matched_gt_boxes[fg_indices],  # (N_fg_rois, 4)
        )
        # fg_pred_deltas 是预测框相对于原始提议框的偏移量 (只针对前景ROI的真实类别)
        # (N_fg_rois, 4)
        loss_box_reg = smooth_l1_loss(
            fg_pred_deltas, gt_pred_deltas, self.smooth_l1_beta, reduction="sum"
        )
        # 归一化边界框回归损失
        box_loss = loss_box_reg / max(class_labels_for_loss.numel(), 1.0)  # 使用采样后的总ROI数归一化

        # 防止 NaN 或 inf 损失
        if not torch.isinf(box_loss).any() and not torch.isnan(box_loss).any():
            loss_dict['bbox_loss'] = box_loss
        else:
            loss_dict['bbox_loss'] = torch.zeros(1, device=self.device).sum() * 0.0  # 赋值一个0损失

        return loss_dict  # 返回包含所有损失的字典

    # --- 步骤 10': 测试模式下的推理结果后处理和返回 ---
    else:
        # 推理时 bs 通常为 1，proposals 列表只有一个元素
        assert len(proposals) == 1
        image_size = proposals[0].image_size  # 获取原始图像尺寸

        # 将 logits 转换为概率分数
        scores = F.softmax(logits, dim=-1)  # (N_rois, T + 1)
        # output = {'scores': scores[:, :-1] } # 暂时只存前景T个类别的分数

        # 将预测的偏移量 (deltas) 应用到原始提议框上，得到最终的预测边界框绝对坐标
        # rois: (N_rois, 5)
        # pred_deltas: (N_rois, T*4)
        predict_boxes_all_T_classes = self.box2box_transform.apply_deltas(
            pred_deltas,  # (N_rois, T*4)
            rois[:, None, 1:].repeat(1, num_active_classes, 1).flatten(1)  # (N_rois, T*4)
            # 将每个ROI复制T次，使其与T个delta对应
        )
        # predict_boxes_all_T_classes: (N_rois, T*4)

        # --- 将 Top-T 类别的分数和框映射回原始的 N_classes 维度 ---
        # full_scores: (N_rois, N_classes + 1) 初始化为0
        full_scores = torch.zeros(len(scores), num_classes + 1, device=self.device)
        # class_indices: (N_rois, T), 包含了每个ROI选中的Top-T类别的原始索引
        # scores: (N_rois, T + 1), 包含了Top-T类别和背景的分数
        # 使用 scatter_ 将 scores 中的T个前景分数放回 full_scores 中对应原始类别索引的位置
        full_scores.scatter_(1, class_indices, scores[:, :-1])  # scores[:, :-1] 是T个前景分数
        full_scores[:, num_classes] = scores[:, -1]  # 将背景分数放到最后一个位置 (索引 N_classes)

        # full_boxes: (N_rois, N_classes * 4) 初始化为0
        full_boxes = torch.zeros(len(scores), num_classes * 4, device=self.device)
        # predict_boxes_all_T_classes: (N_rois, T*4) -> reshape: (N_rois, T, 4)
        predict_boxes_T_reshaped = predict_boxes_all_T_classes.reshape(len(scores), num_active_classes, 4)
        # 使用 scatter_ 将T个类别的预测框放回 full_boxes 中对应原始类别索引的位置
        # class_indices[:, :, None].repeat(1, 1, 4) -> (N_rois, T, 4)
        full_boxes.scatter_(1, class_indices[:, :, None].repeat(1, 1, 4).flatten(1),
                            predict_boxes_T_reshaped.flatten(1))
        # full_boxes 现在是 (N_rois, N_classes * 4)

        # 更新 scores 和 predict_boxes 为映射回 N_classes 维度后的结果
        scores = full_scores  # (N_rois, N_classes + 1)
        # output['scores'] = full_scores[:, :-1] # (N_rois, N_classes)
        predict_boxes = full_boxes  # (N_rois, N_classes * 4)

        # (可选) 将 RPN 的 objectness 得分与分类得分相乘 (或取几何平均)
        if self.mult_rpn_score:
            rpn_obj_scores = proposals[0].objectness_logits  # (N_rois)
            # rpn_obj_scores[rpn_obj_scores < 0] = 0 # 通常 objectness logits 可以是任意值，softmax后是概率
            # 如果要相乘，通常是 rpn_prob * class_prob
            # 这里直接用 logits，可能后续 fast_rcnn_inference 会处理
            # 这里假设 objectness_logits 已经是类似概率的值或经过处理
            # 注意：scores 已经是 softmax 后的概率了
            scores_before_multiply = scores.clone()  # 用于 vis_scores_bf_multiply
            rpn_obj_scores_expanded = rpn_obj_scores[:, None].expand_as(scores)  # (N_rois, N_classes+1)
            # 确保 rpn_obj_scores 是正的，或者将其转换为概率
            # scores = (scores * torch.sigmoid(rpn_obj_scores_expanded)) # 或其他组合方式
            # 源码中是 (scores * rpn_scores[:, None]) ** 0.5,  rpn_scores[rpn_scores < 0] = 0
            # 这意味着 rpn_scores 需要是正值，然后取几何平均
            # 这里我们遵循源码的逻辑，假设 rpn_scores 已经是处理过的
            rpn_obj_scores_clamped = rpn_obj_scores.clamp(min=0)  # 确保非负
            scores = (scores * rpn_obj_scores_clamped[:, None]) ** 0.5  # (N_rois, N_classes+1)
        else:
            scores_before_multiply = scores.clone()

        # 使用 Detectron2 的标准 fast_rcnn_inference 函数进行最终的边界框选择
        # 包括 NMS (非极大值抑制) 和 Top-K 选择
        # 输入:
        #   [predict_boxes]: 列表，每个元素是 (N_rois, N_classes*4) 的张量
        #   [scores]: 列表，每个元素是 (N_rois, N_classes+1) 的张量 (包含背景分数)
        #   [image_size]: 列表，每个元素是 (height, width)
        #   self.test_score_thresh: 得分阈值
        #   self.test_nms_thresh: NMS 的 IoU 阈值
        #   self.test_topk_per_image: 每张图片最终保留的检测数量
        # 返回:
        #   instances: 包含最终检测结果的 Instances 对象列表
        #   _: 通常是保留的索引
        instances, _ = fast_rcnn_inference(
            [predict_boxes],  # (1, N_rois, N_classes*4)
            [scores],  # (1, N_rois, N_classes+1)
            [image_size],
            self.test_score_thresh,
            self.test_nms_thresh,
            False,  # agnostig_cross_classes_nms
            "gaussian",  # nms_method
            0.5,  # gaussian_sigma
            0.01,  # score_threshold_for_vis
            self.test_topk_per_image,
            scores_bf_multiply=[scores_before_multiply],  # 用于可视化的原始分数
            vis=False
        )

        # 对检测结果进行后处理，主要是将边界框坐标缩放回原始图像尺寸
        results = self._postprocess(instances, batched_inputs)
        # output['instances'] = results[0]['instances']
        # 返回一个列表，每个元素是一个字典，包含一张图片的检测结果
        return [{"instances": results[0]["instances"]}]  # 遵循 Detectron2 的输出格式