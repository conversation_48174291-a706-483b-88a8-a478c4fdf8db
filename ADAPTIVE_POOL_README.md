# 灰度图自适应池化与放大工具

这个工具可以将指定文件夹中的灰度图进行自适应池化到7×7大小，并可选择将每个像素用100×100的像素块展示（默认放大到700×700），保存到另一个文件夹中。特别适用于处理特征可视化生成的通道图像。

## 文件说明

- `adaptive_pool_grayscale.py`: 主要的自适应池化脚本
- `test_adaptive_pool.py`: 测试脚本
- `pool_usage_examples.py`: 使用示例和演示脚本生成器
- `ADAPTIVE_POOL_README.md`: 本说明文件

## 环境要求

```bash
pip install torch torchvision opencv-python numpy tqdm
```

## 快速开始

### 1. 测试功能

```bash
# 运行测试脚本验证功能
python test_adaptive_pool.py
```

### 2. 基本使用

```bash
# 处理单张图像 (默认放大到700x700)
python adaptive_pool_grayscale.py --input image.png --output upscaled_image.png

# 批量处理目录 (默认放大到700x700)
python adaptive_pool_grayscale.py --input ./input_dir --output ./output_dir

# 只保存7x7原始尺寸
python adaptive_pool_grayscale.py --input ./input_dir --output ./output_dir --no-upscale
```

### 3. 查看使用示例

```bash
# 查看详细使用示例
python pool_usage_examples.py
```

## 详细使用方法

### 命令行参数

```bash
python adaptive_pool_grayscale.py [参数]
```

**必需参数:**
- `--input, -i`: 输入路径 (文件或目录)
- `--output, -o`: 输出路径 (文件或目录)

**可选参数:**
- `--extensions`: 支持的文件扩展名 (默认: .jpg .jpeg .png .bmp .tiff .tif)
- `--verbose, -v`: 显示详细处理信息
- `--upscale-factor`: 放大倍数 (默认: 100, 即7x7→700x700)
- `--no-upscale`: 不放大，保存原始7x7图像
- `--no-grid`: 不添加网格线到放大图像
- `--save-original`: 同时保存原始7x7图像和放大版本
- `--grid-color`: 网格线颜色 (0-255, 默认: 128)
- `--grid-width`: 网格线宽度 (默认: 1)
- `--help, -h`: 显示帮助信息

### 使用示例

#### 1. 处理单张图像 (默认放大到700×700)

```bash
python adaptive_pool_grayscale.py \
    --input path/to/your/image.png \
    --output upscaled_image_700x700.png \
    --verbose
```

#### 2. 批量处理目录 (默认放大)

```bash
python adaptive_pool_grayscale.py \
    --input ./input_grayscale_images \
    --output ./output_upscaled_images
```

#### 3. 自定义放大倍数

```bash
# 每个像素用50×50像素块显示 (7×7 → 350×350)
python adaptive_pool_grayscale.py \
    --input ./channel_images \
    --output ./pooled_350x350 \
    --upscale-factor 50 \
    --verbose
```

#### 4. 只保存7×7原始尺寸

```bash
python adaptive_pool_grayscale.py \
    --input ./input_images \
    --output ./output_7x7_only \
    --no-upscale
```

#### 5. 无网格线放大

```bash
python adaptive_pool_grayscale.py \
    --input ./input_images \
    --output ./output_smooth \
    --no-grid
```

#### 6. 同时保存两种版本

```bash
# 同时保存7×7和700×700版本
python adaptive_pool_grayscale.py \
    --input ./input_images \
    --output ./output_both \
    --save-original
```

#### 7. 处理特征通道图像

```bash
# 处理从backbone提取的特征通道，放大便于查看
python adaptive_pool_grayscale.py \
    --input ./feature_visualizations/image_name_channels \
    --output ./upscaled_features_700x700 \
    --extensions .png
```

## 功能特性

### ✅ **自适应池化**
- 使用PyTorch的`F.adaptive_avg_pool2d`
- 将任意尺寸的图像池化到7×7大小
- 保持图像的主要特征信息

### ✅ **灵活输入**
- 支持单张图像或整个目录
- 支持多种图像格式 (PNG, JPG, BMP, TIFF等)
- 自动识别灰度图像

### ✅ **批量处理**
- 进度条显示处理进度
- 错误处理和日志记录
- 保持原始文件名

### ✅ **质量保证**
- 数值范围检查 (0-255)
- 文件完整性验证
- 详细的错误报告

## 输出结果

### 输入输出对比

| 输入尺寸 | 输出尺寸 | 压缩比例 |
|---------|---------|---------|
| 64×64 | 7×7 | 83.7:1 |
| 128×96 | 7×7 | 251:1 |
| 256×192 | 7×7 | 998:1 |
| 任意尺寸 | 7×7 | 自适应 |

### 文件结构示例

```
输入目录/
├── channel_000.png (256×192)
├── channel_001.png (256×192)
├── channel_002.png (256×192)
└── ...

输出目录/
├── channel_000.png (7×7)
├── channel_001.png (7×7)
├── channel_002.png (7×7)
└── ...
```

## 应用场景

### 1. **特征可视化后处理**
- 将backbone特征通道池化到固定尺寸
- 便于后续的ROI特征提取
- 减少存储空间和计算量

### 2. **数据预处理**
- 统一图像尺寸用于机器学习
- 创建缩略图或预览图
- 数据增强的预处理步骤

### 3. **特征分析**
- 将高分辨率特征图压缩到可分析的尺寸
- 保持空间结构信息
- 便于特征比较和可视化

## 技术细节

### 自适应池化原理

```python
# 核心实现
pooled = F.adaptive_avg_pool2d(image_tensor, (7, 7))
```

- **自适应**: 自动计算池化核大小和步长
- **平均池化**: 保持图像的平均特征值
- **固定输出**: 无论输入尺寸如何，输出始终为7×7

### 数据流程

1. **加载**: 读取灰度图像 → tensor [1, 1, H, W]
2. **池化**: 自适应池化 → tensor [1, 1, 7, 7]
3. **保存**: 转换回numpy → 保存为图像文件

## 性能优化

### 内存使用
- 逐张处理，避免内存溢出
- 及时释放tensor内存
- 支持大批量图像处理

### 处理速度
- GPU加速 (如果可用)
- 批量文件操作
- 进度条显示处理状态

## 故障排除

### 常见问题

1. **图像读取失败**
   ```
   Error: Cannot read image: path/to/image.png
   ```
   - 检查文件路径是否正确
   - 确认文件格式是否支持
   - 验证文件是否损坏

2. **输出目录权限问题**
   ```
   PermissionError: [Errno 13] Permission denied
   ```
   - 检查输出目录的写入权限
   - 使用管理员权限运行

3. **内存不足**
   ```
   RuntimeError: CUDA out of memory
   ```
   - 使用CPU处理: 修改代码中的device设置
   - 减少批量处理的图像数量

### 调试技巧

```bash
# 使用详细模式查看处理过程
python adaptive_pool_grayscale.py --input image.png --output output.png --verbose

# 测试单张图像
python adaptive_pool_grayscale.py --input test.png --output test_7x7.png

# 检查输出图像尺寸
python -c "import cv2; img=cv2.imread('test_7x7.png', 0); print(f'Shape: {img.shape}')"
```

## 扩展功能

### 自定义池化尺寸

如需修改输出尺寸，编辑脚本中的池化函数：

```python
# 修改为其他尺寸，例如14x14
pooled = F.adaptive_avg_pool2d(image_tensor, (14, 14))
```

### 支持彩色图像

修改加载函数以支持RGB图像：

```python
# 读取彩色图像
image = cv2.imread(image_path, cv2.IMREAD_COLOR)
image_tensor = torch.from_numpy(image).float().permute(2, 0, 1).unsqueeze(0)
```

## 许可证

本工具基于MIT许可证开源，可自由使用和修改。
