#!/usr/bin/env python3
"""
DE-ViT模型的CAM (Class Activation Mapping) 可视化
结合传播层特征和类激活映射
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import torch
import torch.nn.functional as F
import numpy as np
import cv2
import matplotlib.pyplot as plt
from pathlib import Path
import argparse
from detectron2.config import get_cfg
import detectron2.data.transforms as T
import detectron2.data.detection_utils as utils
from tools.train_net import Trainer

class DEViTCAMExtractor:
    """DE-ViT模型的CAM特征提取器"""
    
    def __init__(self):
        self.propagation_features = {}
        self.gradients = {}
        self.hooks = []
        self.target_class = None
    
    def register_hooks(self, model):
        """注册hooks用于CAM计算"""
        print("Registering CAM hooks...")
        
        # 查找传播层
        for name, module in model.named_modules():
            if 'per_cls_cnn' in name and hasattr(module, 'main_layers'):
                print(f"Found propagation network: {name}")
                
                # 为每个传播层注册hook
                for i, layer in enumerate(module.main_layers):
                    layer_name = f"{name}.main_layers[{i}]"
                    
                    # 前向hook - 保存特征
                    forward_hook = layer.register_forward_hook(
                        lambda m, inp, out, ln=layer_name: self._save_features(m, inp, out, ln)
                    )
                    self.hooks.append(forward_hook)
                    
                    # 反向hook - 保存梯度
                    backward_hook = layer.register_backward_hook(
                        lambda m, inp, out, ln=layer_name: self._save_gradients(m, inp, out, ln)
                    )
                    self.hooks.append(backward_hook)
                    
                    print(f"  ✓ Registered hooks for layer {i}")
                
                break
        
        return len(self.hooks) > 0
    
    def _save_features(self, module, input, output, layer_name):
        """保存前向传播特征"""
        if isinstance(output, torch.Tensor):
            self.propagation_features[layer_name] = output
    
    def _save_gradients(self, module, grad_input, grad_output, layer_name):
        """保存反向传播梯度"""
        if grad_output[0] is not None:
            self.gradients[layer_name] = grad_output[0]
    
    def generate_cam(self, layer_name, target_size=None):
        """生成指定层的CAM"""
        if layer_name not in self.propagation_features or layer_name not in self.gradients:
            print(f"Missing features or gradients for {layer_name}")
            return None
        
        features = self.propagation_features[layer_name]  # [B, C, H, W]
        gradients = self.gradients[layer_name]  # [B, C, H, W]
        
        # 计算权重 (Grad-CAM方法)
        weights = torch.mean(gradients, dim=(2, 3), keepdim=True)  # [B, C, 1, 1]
        
        # 加权组合特征图
        cam = torch.sum(weights * features, dim=1, keepdim=True)  # [B, 1, H, W]
        
        # ReLU激活
        cam = F.relu(cam)
        
        # 归一化
        cam = cam.squeeze()  # [H, W]
        if cam.max() > cam.min():
            cam = (cam - cam.min()) / (cam.max() - cam.min())
        
        # 调整尺寸
        if target_size is not None:
            cam = F.interpolate(
                cam.unsqueeze(0).unsqueeze(0), 
                size=target_size, 
                mode='bilinear', 
                align_corners=False
            ).squeeze()
        
        return cam.detach().cpu().numpy()
    
    def cleanup(self):
        """清理hooks"""
        for hook in self.hooks:
            hook.remove()
        self.hooks.clear()

def setup_model_and_config(model_weights, rpn_config, device='cuda'):
    """设置模型和配置"""
    config = get_cfg()
    config.merge_from_file('configs/few-shot/vitb_shot10.yaml')
    
    config.MODEL.WEIGHTS = model_weights
    config.DE.OFFLINE_RPN_CONFIG = rpn_config
    config.MODEL.DEVICE = device
    config.MODEL.MASK_ON = True
    config.MODEL.ROI_HEADS.NUM_CLASSES = 80
    
    config.freeze()
    
    # 加载模型
    model = Trainer.build_model(config).to(device)
    checkpoint = torch.load(model_weights, map_location=device)
    
    if 'model' in checkpoint:
        state_dict = checkpoint['model']
    else:
        state_dict = checkpoint
    
    try:
        model.load_state_dict(state_dict, strict=False)
    except:
        print("Using flexible loading...")
        model_keys = set(model.state_dict().keys())
        filtered_dict = {k: v for k, v in state_dict.items() if k in model_keys}
        model.load_state_dict(filtered_dict, strict=False)
    
    model.eval()
    return model, config

def preprocess_image_for_cam(image_path, config):
    """为CAM预处理图像"""
    image = utils.read_image(image_path, format="RGB")
    height, width = image.shape[0], image.shape[1]
    
    augs = utils.build_augmentation(config, False)
    augmentations = T.AugmentationList(augs)
    
    aug_input = T.AugInput(image)
    augmentations(aug_input)
    
    dataset_dict = {
        "height": height,
        "width": width,
        "image": torch.as_tensor(np.ascontiguousarray(aug_input.image.transpose(2, 0, 1)))
    }
    
    return [dataset_dict], image, (height, width)

def visualize_cam_results(original_image, cam_maps, layer_names, output_dir):
    """可视化CAM结果"""
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    # 原始图像尺寸
    img_height, img_width = original_image.shape[:2]
    
    for i, (layer_name, cam) in enumerate(zip(layer_names, cam_maps)):
        if cam is None:
            continue
        
        # 调整CAM尺寸到原图大小
        cam_resized = cv2.resize(cam, (img_width, img_height))
        
        # 创建热力图
        heatmap = cv2.applyColorMap(np.uint8(255 * cam_resized), cv2.COLORMAP_JET)
        heatmap = cv2.cvtColor(heatmap, cv2.COLOR_BGR2RGB)
        
        # 叠加到原图
        overlay = 0.6 * original_image + 0.4 * heatmap
        overlay = np.clip(overlay, 0, 255).astype(np.uint8)
        
        # 创建对比图
        fig, axes = plt.subplots(1, 4, figsize=(16, 4))
        
        # 原图
        axes[0].imshow(original_image)
        axes[0].set_title('Original Image')
        axes[0].axis('off')
        
        # CAM热力图
        axes[1].imshow(cam_resized, cmap='jet')
        axes[1].set_title(f'CAM - {layer_name}')
        axes[1].axis('off')
        
        # 热力图
        axes[2].imshow(heatmap)
        axes[2].set_title('Heatmap')
        axes[2].axis('off')
        
        # 叠加图
        axes[3].imshow(overlay)
        axes[3].set_title('Overlay')
        axes[3].axis('off')
        
        plt.suptitle(f'DE-ViT CAM Visualization - Propagation Layer {i}', fontsize=14)
        plt.tight_layout()
        
        # 保存
        safe_name = layer_name.replace('.', '_').replace('/', '_')
        output_file = output_path / f"cam_{safe_name}.png"
        plt.savefig(output_file, dpi=150, bbox_inches='tight')
        plt.close()
        
        print(f"✓ Saved CAM visualization: {output_file}")
        
        # 单独保存CAM
        cam_file = output_path / f"cam_only_{safe_name}.png"
        plt.figure(figsize=(8, 8))
        plt.imshow(cam_resized, cmap='jet')
        plt.title(f'CAM - {layer_name}')
        plt.axis('off')
        plt.colorbar()
        plt.savefig(cam_file, dpi=150, bbox_inches='tight')
        plt.close()

def main():
    parser = argparse.ArgumentParser(description='DE-ViT CAM Visualization')
    parser.add_argument('--image', type=str, required=True, help='Test image path')
    parser.add_argument('--model-weights', type=str, required=True, help='Model weights path')
    parser.add_argument('--rpn-config', type=str, required=True, help='RPN config path')
    parser.add_argument('--output', type=str, default='devit_cam_results', help='Output directory')
    parser.add_argument('--device', type=str, default='cuda', choices=['cuda', 'cpu'])
    parser.add_argument('--target-class', type=int, default=None, help='Target class for CAM')
    
    args = parser.parse_args()
    
    if args.device == 'cuda' and not torch.cuda.is_available():
        args.device = 'cpu'
    
    cam_extractor = DEViTCAMExtractor()
    
    try:
        print("DE-ViT CAM (Class Activation Mapping) Visualization")
        print("=" * 55)
        
        # 1. 设置模型
        model, config = setup_model_and_config(args.model_weights, args.rpn_config, args.device)
        
        # 2. 注册CAM hooks
        if not cam_extractor.register_hooks(model):
            print("❌ Failed to register CAM hooks!")
            return 1
        
        # 3. 预处理图像
        batched_inputs, original_image, img_size = preprocess_image_for_cam(args.image, config)
        
        # 4. 前向传播
        print("Running forward pass...")
        for item in batched_inputs:
            item["image"] = item["image"].to(args.device).requires_grad_(True)
        
        outputs = model(batched_inputs)
        
        # 5. 反向传播生成梯度
        print("Computing gradients for CAM...")
        
        # 获取预测结果
        if isinstance(outputs, list):
            predictions = outputs[0]
        else:
            predictions = outputs
        
        # 选择目标类别
        if args.target_class is not None:
            target_score = predictions['instances'].scores[args.target_class]
        else:
            # 使用最高分数的预测
            target_score = predictions['instances'].scores.max()
        
        # 反向传播
        target_score.backward(retain_graph=True)
        
        # 6. 生成CAM
        print("Generating CAM visualizations...")
        layer_names = list(cam_extractor.propagation_features.keys())
        cam_maps = []
        
        for layer_name in layer_names:
            cam = cam_extractor.generate_cam(layer_name, target_size=img_size)
            cam_maps.append(cam)
            if cam is not None:
                print(f"✓ Generated CAM for {layer_name}")
        
        # 7. 可视化结果
        visualize_cam_results(original_image, cam_maps, layer_names, args.output)
        
        print(f"\n🎉 CAM visualization completed!")
        print(f"Output directory: {args.output}")
        print(f"Generated CAM for {len([c for c in cam_maps if c is not None])} layers")
        
        return 0
        
    except Exception as e:
        print(f"💥 Error: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    finally:
        cam_extractor.cleanup()

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
