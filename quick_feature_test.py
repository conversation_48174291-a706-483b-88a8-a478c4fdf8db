#!/usr/bin/env python3
"""
快速测试脚本 - 验证传播层特征提取
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import torch
import numpy as np
from detectron2.config import get_cfg
import detectron2.data.transforms as T
import detectron2.data.detection_utils as utils
from tools.train_net import Trainer

def quick_test():
    """快速测试传播层特征提取"""

    # 1. 设置配置
    config = get_cfg()
    config.merge_from_file('configs/few-shot/vitb_shot10.yaml')
    config.MODEL.DEVICE = 'cpu'  # 使用CPU避免CUDA问题
    config.MODEL.WEIGHTS = ''  # 暂时不加载权重
    config.MODEL.MASK_ON = True

    # 修复OFFLINE_RPN_CONFIG问题
    if config.DE.OFFLINE_RPN_CONFIG is None or config.DE.OFFLINE_RPN_CONFIG == '':
        config.DE.OFFLINE_RPN_CONFIG = "configs/RPN/mask_rcnn_R_50_FPN_1x.yaml"
        print(f"Set OFFLINE_RPN_CONFIG to: {config.DE.OFFLINE_RPN_CONFIG}")

    config.freeze()
    
    # 2. 创建模型 (不加载权重)
    print("Creating model...")
    model = Trainer.build_model(config)
    model.eval()
    
    # 3. 查找传播层
    print("\nSearching for propagation layers:")
    propagation_layers = []
    
    for name, module in model.named_modules():
        if 'per_cls_cnn' in name:
            print(f"Found: {name} - {type(module)}")
            if hasattr(module, 'main_layers'):
                print(f"  Has main_layers: {len(module.main_layers)} layers")
                for i, layer in enumerate(module.main_layers):
                    print(f"    Layer {i}: {layer}")
                propagation_layers.append((name, module))
    
    if not propagation_layers:
        print("❌ No propagation layers found!")
        return False
    
    # 4. 注册Hook到第一个传播层
    print(f"\nRegistering hook to first propagation layer...")
    
    captured_features = {}
    
    def hook_fn(module, input, output):
        captured_features['first_layer'] = output.detach()
        print(f"✓ Captured features: {output.shape}")
    
    first_prop_name, first_prop_module = propagation_layers[0]
    first_layer = first_prop_module.main_layers[0]
    hook = first_layer.register_forward_hook(hook_fn)
    
    print(f"Hook registered to: {first_prop_name}.main_layers[0]")
    
    # 5. 创建虚拟输入测试
    print(f"\nTesting with dummy input...")
    
    # 创建虚拟的ROI特征 (模拟传播层的输入)
    batch_size = 2
    channels = 256  # 假设的输入通道数
    height, width = 14, 14  # ROI特征图尺寸
    
    dummy_input = torch.randn(batch_size, channels, height, width)
    
    try:
        # 直接调用传播层
        with torch.no_grad():
            output = first_prop_module(dummy_input)
        
        print(f"✓ Propagation layer forward pass successful!")
        print(f"Input shape: {dummy_input.shape}")
        print(f"Output shape: {output.shape if isinstance(output, torch.Tensor) else type(output)}")
        
        if 'first_layer' in captured_features:
            features = captured_features['first_layer']
            print(f"✓ Captured first layer features: {features.shape}")
            print(f"Feature statistics:")
            print(f"  Min: {features.min().item():.4f}")
            print(f"  Max: {features.max().item():.4f}")
            print(f"  Mean: {features.mean().item():.4f}")
            print(f"  Std: {features.std().item():.4f}")
            
            return True
        else:
            print("❌ No features captured by hook")
            return False
            
    except Exception as e:
        print(f"❌ Error during forward pass: {e}")
        return False
    
    finally:
        hook.remove()
        print("Hook cleaned up")

def main():
    print("Quick Feature Extraction Test")
    print("=" * 40)
    
    try:
        success = quick_test()
        
        if success:
            print("\n🎉 Test successful!")
            print("The hook mechanism should work for feature extraction.")
            print("\nNext steps:")
            print("1. Use visualize_propagation_hook.py with your trained model")
            print("2. Provide a real test image")
            print("3. Specify the correct model weights path")
        else:
            print("\n❌ Test failed!")
            print("The hook mechanism may need adjustment.")
            
    except Exception as e:
        print(f"\n💥 Test crashed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
