@classmethod
def from_config(cls, cfg, use_bn=False):
    # --- 1. 配置和构建离线 RPN (Offline RPN) ---
    offline_cfg = get_cfg()  # 获取一个 Detectron2 的标准配置对象
    offline_cfg.merge_from_file(cfg.DE.OFFLINE_RPN_CONFIG)  # 从主配置文件中指定的 RPN 配置文件路径加载 RPN 的配置

    # 根据主配置中的参数，有条件地修改离线 RPN 的配置
    if cfg.DE.OFFLINE_RPN_LSJ_PRETRAINED:  # 如果使用了 LSJ (Large-Scale Jittering) 预训练的 RPN
        offline_cfg.MODEL.BACKBONE.FREEZE_AT = 0  # 通常意味着解冻所有层以便使用 SyncBN
        offline_cfg.MODEL.RESNETS.NORM = "BN"  # 设置 ResNet 骨干网络的归一化层为 BN (Batch Normalization)
        offline_cfg.MODEL.FPN.NORM = "BN"  # 设置 FPN (Feature Pyramid Network) 的归一化层为 BN
        # offline_cfg.MODEL.RESNETS.NORM = "SyncBN" # 注释掉的是使用 SyncBN 的选项
        # offline_cfg.MODEL.FPN.NORM = "SyncBN"
        offline_cfg.MODEL.RPN.CONV_DIMS = [-1, -1]  # RPN 卷积层的维度设置
    if cfg.DE.OFFLINE_RPN_NMS_THRESH:
        offline_cfg.MODEL.RPN.NMS_THRESH = cfg.DE.OFFLINE_RPN_NMS_THRESH  # 设置 RPN 的 NMS (非极大值抑制) 阈值
    if cfg.DE.OFFLINE_RPN_POST_NMS_TOPK_TEST:
        offline_cfg.MODEL.RPN.POST_NMS_TOPK_TEST = cfg.DE.OFFLINE_RPN_POST_NMS_TOPK_TEST  # 设置 RPN 在 NMS 后保留的最高分提议数量

    # 使用修改后的离线配置构建 RPN 的骨干网络和 RPN 本身
    offline_backbone = build_backbone(offline_cfg)
    offline_rpn = build_proposal_generator(offline_cfg, offline_backbone.output_shape())

    # 将离线骨干网络和 RPN 设置为评估模式 (eval mode) 并禁用梯度计算
    # 因为它们是预训练好的，在 DE-ViT 训练中不进行更新
    for p in offline_backbone.parameters(): p.requires_grad = False
    for p in offline_rpn.parameters(): p.requires_grad = False
    offline_backbone.eval()
    offline_rpn.eval()

    # --- 2. 构建主骨干网络 (DE-ViT 使用的 ViT) ---
    backbone = build_backbone(cfg)  # 使用主配置文件 cfg 构建骨干网络 (例如 DINOv2 ViT)
    # 将主骨干网络也设置为评估模式并禁用梯度计算，因为论文提到 ViT 骨干是冻结的
    for p in backbone.parameters(): p.requires_grad = False
    backbone.eval()

    # 定义不同 ViT 型号的特征维度
    feat_dims = {'large': 1024, 'base': 768, 'small': 384}

    # --- 3. 构建并返回传递给 __init__ 方法的参数字典 ---
    return {
        "backbone": backbone,  # 主 ViT 骨干网络
        "pixel_mean": cfg.MODEL.PIXEL_MEAN,  # 主骨干网络图像归一化均值
        "pixel_std": cfg.MODEL.PIXEL_STD,  # 主骨干网络图像归一化标准差
        "class_prototypes_file": cfg.DE.CLASS_PROTOTYPES,  # 类别原型文件路径
        "bg_prototypes_file": cfg.DE.BG_PROTOTYPES,  # 背景原型文件路径

        "roialign_size": cfg.MODEL.ROI_BOX_HEAD.POOLER_RESOLUTION,  # ROI Align 输出尺寸

        "offline_backbone": offline_backbone,  # 离线 RPN 的骨干网络
        "offline_proposal_generator": offline_rpn,  # 离线 RPN
        "offline_input_format": offline_cfg.INPUT.FORMAT if offline_cfg else None,  # 离线 RPN 的输入图像格式
        "offline_pixel_mean": offline_cfg.MODEL.PIXEL_MEAN if offline_cfg else None,  # 离线 RPN 的图像归一化均值
        "offline_pixel_std": offline_cfg.MODEL.PIXEL_STD if offline_cfg else None,  # 离线 RPN 的图像归一化标准差

        # 实例化一个 Matcher 对象，用于在训练时匹配提议框和真实框
        "proposal_matcher": Matcher(
            cfg.MODEL.ROI_HEADS.IOU_THRESHOLDS,  # Matcher 使用的 IoU 阈值
            cfg.MODEL.ROI_HEADS.IOU_LABELS,  # Matcher 使用的 IoU 标签
            allow_low_quality_matches=False,
        ),

        # 边界框回归相关参数
        "box2box_transform": Box2BoxTransform(weights=cfg.MODEL.ROI_BOX_HEAD.BBOX_REG_WEIGHTS),  # 边界框变换器
        "smooth_l1_beta": cfg.MODEL.ROI_BOX_HEAD.SMOOTH_L1_BETA,  # Smooth L1 损失的 beta 参数

        # 测试时参数
        "test_score_thresh": cfg.MODEL.ROI_HEADS.SCORE_THRESH_TEST,  # 得分阈值
        "test_nms_thresh": cfg.MODEL.ROI_HEADS.NMS_THRESH_TEST,  # NMS 阈值
        "test_topk_per_image": cfg.TEST.DETECTIONS_PER_IMAGE,  # 每张图片最多检测数量

        "box_noise_scale": 0.5,  # 边界框噪声尺度 (注意 __init__ 中默认是 1.0，这里会覆盖)
        "cls_temp": cfg.DE.TEMP,  # 分类温度系数

        # Top-T 选择的类别数量 (对应配置文件中的 DE.TOPK)
        "num_sample_class": cfg.DE.TOPK,

        # 从预定义的字典中根据数据集名称获取基础类别和所有类别的列表
        "seen_cids": SEEN_CLS_DICT[cfg.DATASETS.TRAIN[0]],
        "all_cids": ALL_CLS_DICT[cfg.DATASETS.TRAIN[0]],

        # 用于 channel-reorder 插值后的目标通道长度 (对应配置文件中的 DE.T)
        # 注意：这里的 cfg.DE.T 与 cfg.DE.TOPK 可能含义不同，需要看具体使用
        # T_length 更像是 channel-reorder 中插值的目标长度
        # DE.TOPK 是选择多少个类别进行后续处理
        "T_length": cfg.DE.T,

        # 特征维度相关
        "input_feat_dim": feat_dims[cfg.MODEL.BACKBONE.TYPE],  # 根据骨干网络类型获取输入特征维度
        "proj_feat_dim": 256,  # 投影后特征维度 (硬编码)
        "pos_emb_size": 128,  # 位置嵌入维度 (硬编码)
        "dist_emb_size": 64,  # 距离/相似度嵌入维度 (硬编码)
        "hidden_size": 256,  # 区域传播网络隐藏层维度 (硬编码)

        "bg_cls_weight": cfg.DE.BG_CLS_LOSS_WEIGHT,  # 背景分类损失权重
        "batch_size_per_image": cfg.DE.RCNN_BATCH_SIZE,  # 每张图片的 RCNN 批大小 (用于采样)
        "pos_ratio": cfg.DE.POS_RATIO,  # 正样本比例

        "mult_rpn_score": cfg.DE.MULTIPLY_RPN_SCORE,  # 是否乘以 RPN 得分
        "num_layers": cfg.DE.NUM_CLS_LAYERS,  # 区域传播网络的层数
    }