# DINO-CAM分类器配置
# 使用与vitb_shot10.yaml相同的基类进行训练

# 基类信息 (来自fs_coco14_base_train)
BASE_CLASSES:
  NAMES: [
    'person', 'bicycle', 'car', 'motorcycle', 'train', 'truck',
    'boat', 'bench', 'bird', 'horse', 'sheep', 'bear', 'zebra', 'giraffe',
    'backpack', 'handbag', 'suitcase', 'frisbee', 'skis', 'kite', 'surfboard',
    'bottle', 'fork', 'spoon', 'bowl', 'banana', 'apple', 'sandwich', 'orange',
    'broccoli', 'carrot', 'pizza', 'donut', 'chair', 'bed', 'toilet', 'tv',
    'laptop', 'mouse', 'remote', 'microwave', 'oven', 'toaster',
    'refrigerator', 'book', 'clock', 'vase', 'toothbrush'
  ]
  NUM_CLASSES: 48

# DINO配置 (与vitb_shot10.yaml保持一致)
DINO:
  ARCHITECTURE: "vit_base"  # 与vitb_shot10.yaml中的TYPE: "base"对应
  PRETRAINED_WEIGHTS: "weights/initial/dino/dino_vitbase14_pretrain.pth"
  OUTPUT_DIM: 768  # ViT-Base的输出维度
  FROZEN: True     # 冻结DINO参数

# CAM分类器配置
CAM_CLASSIFIER:
  FEATURE_DIM: 512        # CAM特征维度
  CONV_KERNEL_SIZE: 3     # 卷积核大小
  CONV_PADDING: 1         # 卷积padding
  USE_BATCH_NORM: True    # 使用BatchNorm
  DROPOUT: 0.0            # Dropout率

# 数据配置
DATA:
  DATASET_DIR: "./base_class_dataset"
  IMAGE_SIZE: 224
  BATCH_SIZE: 32
  NUM_WORKERS: 4
  
  # 数据增强 (与vitb_shot10.yaml的预处理保持一致)
  PIXEL_MEAN: [0.48145466, 0.4578275, 0.40821073]  # 与vitb_shot10.yaml一致
  PIXEL_STD: [0.26862954, 0.26130258, 0.27577711]   # 与vitb_shot10.yaml一致
  
  TRAIN_TRANSFORMS:
    - RESIZE: 256
    - RANDOM_CROP: 224
    - RANDOM_HORIZONTAL_FLIP: 0.5
    - COLOR_JITTER:
        brightness: 0.1
        contrast: 0.1
        saturation: 0.1
        hue: 0.05
    - NORMALIZE:
        mean: [0.48145466, 0.4578275, 0.40821073]
        std: [0.26862954, 0.26130258, 0.27577711]
  
  VAL_TRANSFORMS:
    - RESIZE: 256
    - CENTER_CROP: 224
    - NORMALIZE:
        mean: [0.48145466, 0.4578275, 0.40821073]
        std: [0.26862954, 0.26130258, 0.27577711]

# 训练配置
TRAINING:
  EPOCHS: 100
  LEARNING_RATE: 0.001
  WEIGHT_DECAY: 1e-4
  OPTIMIZER: "Adam"
  
  # 学习率调度
  LR_SCHEDULER:
    TYPE: "StepLR"
    STEP_SIZE: 30
    GAMMA: 0.1
  
  # 早停
  EARLY_STOPPING:
    PATIENCE: 10
    MIN_DELTA: 0.001

# 验证配置
VALIDATION:
  EVAL_PERIOD: 1  # 每个epoch验证一次
  SAVE_BEST: True
  METRICS: ["accuracy", "top5_accuracy"]

# 输出配置
OUTPUT:
  SAVE_DIR: "./checkpoints/dino_cam_base_classes"
  LOG_PERIOD: 100  # 每100个batch打印一次
  CHECKPOINT_PERIOD: 10  # 每10个epoch保存一次检查点

# CAM可视化配置
CAM_VISUALIZATION:
  SAVE_SAMPLES: True
  NUM_SAMPLES: 10  # 每个类别保存的CAM样本数
  OUTPUT_DIR: "./cam_visualizations"
  
# 与DE-ViT Few-Shot的对应关系
CORRESPONDENCE:
  SOURCE_CONFIG: "configs/few-shot/vitb_shot10.yaml"
  SOURCE_DATASET: "fs_coco14_base_train"
  DINO_COMPATIBILITY: True  # 确保与DE-ViT中的DINO兼容
