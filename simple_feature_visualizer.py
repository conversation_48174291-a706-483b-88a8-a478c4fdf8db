#!/usr/bin/env python3
"""
简化版DINOv2特征可视化工具
避免循环导入问题
"""

import os
import sys
import torch
import torch.nn as nn
import numpy as np
import cv2
import matplotlib.pyplot as plt
import argparse
from pathlib import Path

def create_simple_vit_base():
    """
    创建简化的ViT-Base模型，避免导入问题
    """
    class PatchEmbed(nn.Module):
        def __init__(self, img_size=518, patch_size=14, in_chans=3, embed_dim=768):
            super().__init__()
            self.img_size = img_size
            self.patch_size = patch_size
            self.proj = nn.Conv2d(in_chans, embed_dim, kernel_size=patch_size, stride=patch_size)
            
        def forward(self, x):
            x = self.proj(x)  # B C H W
            H, W = x.size(2), x.size(3)
            x = x.flatten(2).transpose(1, 2)  # B HW C
            return x, H, W
    
    class Attention(nn.Module):
        def __init__(self, dim, num_heads=12):
            super().__init__()
            self.num_heads = num_heads
            self.head_dim = dim // num_heads
            self.scale = self.head_dim ** -0.5
            self.qkv = nn.Linear(dim, dim * 3, bias=True)
            self.proj = nn.Linear(dim, dim)
            
        def forward(self, x):
            B, N, C = x.shape
            qkv = self.qkv(x).reshape(B, N, 3, self.num_heads, self.head_dim).permute(2, 0, 3, 1, 4)
            q, k, v = qkv[0], qkv[1], qkv[2]
            
            attn = (q @ k.transpose(-2, -1)) * self.scale
            attn = attn.softmax(dim=-1)
            
            x = (attn @ v).transpose(1, 2).reshape(B, N, C)
            x = self.proj(x)
            return x
    
    class MLP(nn.Module):
        def __init__(self, in_features, hidden_features):
            super().__init__()
            self.fc1 = nn.Linear(in_features, hidden_features)
            self.act = nn.GELU()
            self.fc2 = nn.Linear(hidden_features, in_features)
            
        def forward(self, x):
            return self.fc2(self.act(self.fc1(x)))
    
    class TransformerBlock(nn.Module):
        def __init__(self, dim, num_heads, mlp_ratio=4.0):
            super().__init__()
            self.norm1 = nn.LayerNorm(dim)
            self.attn = Attention(dim, num_heads)
            self.norm2 = nn.LayerNorm(dim)
            self.mlp = MLP(dim, int(dim * mlp_ratio))
            
        def forward(self, x):
            x = x + self.attn(self.norm1(x))
            x = x + self.mlp(self.norm2(x))
            return x
    
    class SimpleViT(nn.Module):
        def __init__(self):
            super().__init__()
            self.patch_embed = PatchEmbed(518, 14, 3, 768)

            # CLS token
            self.cls_token = nn.Parameter(torch.zeros(1, 1, 768))

            # 基础位置编码 (用于插值)
            base_size = 518 // 14  # 37
            self.base_pos_embed = nn.Parameter(torch.zeros(1, base_size * base_size + 1, 768))

            # 12层Transformer
            self.blocks = nn.ModuleList([
                TransformerBlock(768, 12, 4.0) for _ in range(12)
            ])
            self.norm = nn.LayerNorm(768)

            # 初始化
            nn.init.trunc_normal_(self.base_pos_embed, std=0.02)
            nn.init.trunc_normal_(self.cls_token, std=0.02)

        def interpolate_pos_encoding(self, x, H, W):
            """插值位置编码以适应不同的输入尺寸"""
            N = x.shape[1] - 1  # 去除CLS token
            base_size = int(self.base_pos_embed.shape[1] - 1) ** 0.5  # 37

            if N == self.base_pos_embed.shape[1] - 1 and H == W:
                return self.base_pos_embed

            # 分离CLS token和patch位置编码
            class_pos_embed = self.base_pos_embed[:, 0:1]
            patch_pos_embed = self.base_pos_embed[:, 1:]

            # 计算当前的patch网格大小
            patch_h = H
            patch_w = W

            # 重新整形并插值
            patch_pos_embed = patch_pos_embed.reshape(1, int(base_size), int(base_size), 768).permute(0, 3, 1, 2)
            patch_pos_embed = nn.functional.interpolate(
                patch_pos_embed,
                size=(patch_h, patch_w),
                mode='bicubic',
                align_corners=False
            )
            patch_pos_embed = patch_pos_embed.permute(0, 2, 3, 1).reshape(1, patch_h * patch_w, 768)

            # 合并CLS token和patch位置编码
            pos_embed = torch.cat([class_pos_embed, patch_pos_embed], dim=1)

            return pos_embed

        def forward(self, x):
            B = x.shape[0]

            # Patch embedding
            x, H, W = self.patch_embed(x)

            # 添加CLS token
            cls_tokens = self.cls_token.expand(B, -1, -1)
            x = torch.cat((cls_tokens, x), dim=1)

            # 获取适应当前尺寸的位置编码
            pos_embed = self.interpolate_pos_encoding(x, H, W)

            # 添加位置编码
            x = x + pos_embed

            # Transformer blocks
            for blk in self.blocks:
                x = blk(x)

            x = self.norm(x)

            # 返回patch tokens (去除CLS token)
            patch_tokens = x[:, 1:]

            return patch_tokens, H, W
    
    return SimpleViT()


def preprocess_image(image_path, target_size=800, max_size=1333):
    """预处理图像"""
    # 读取图像
    image = cv2.imread(image_path)
    if image is None:
        raise ValueError(f"Cannot read image: {image_path}")
    
    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    print(f"Original image shape: {image.shape}")
    
    # 调整尺寸
    h, w = image.shape[:2]
    scale = target_size * 1.0 / min(h, w)
    if h < w:
        newh, neww = target_size, int(scale * w + 0.5)
    else:
        newh, neww = int(scale * h + 0.5), target_size
    
    if max(newh, neww) > max_size:
        scale = max_size * 1.0 / max(newh, neww)
        newh = int(newh * scale + 0.5)
        neww = int(neww * scale + 0.5)
    
    image_resized = cv2.resize(image, (neww, newh))
    print(f"Resized image shape: {image_resized.shape}")
    
    # 转换为tensor并归一化
    image_tensor = torch.from_numpy(image_resized).float().permute(2, 0, 1) / 255.0
    
    # ImageNet标准化
    mean = torch.tensor([0.485, 0.456, 0.406]).view(3, 1, 1)
    std = torch.tensor([0.229, 0.224, 0.225]).view(3, 1, 1)
    image_tensor = (image_tensor - mean) / std
    
    return image_tensor.unsqueeze(0), (newh, neww)


def visualize_features(features, patch_grid_size, output_dir, image_name):
    """可视化特征"""
    output_dir = Path(output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)

    B, N, C = features.shape
    patch_h, patch_w = patch_grid_size

    print(f"Patch grid: {patch_h} x {patch_w}")
    print(f"Expected patches: {patch_h * patch_w}, Actual: {N}")

    # 确保patch数量匹配
    expected_patches = patch_h * patch_w
    if N != expected_patches:
        if N > expected_patches:
            # 裁剪多余的patches
            features = features[:, :expected_patches, :]
            print(f"Cropped features from {N} to {expected_patches}")
        else:
            # 填充不足的patches
            padding = torch.zeros(B, expected_patches - N, C, device=features.device)
            features = torch.cat([features, padding], dim=1)
            print(f"Padded features from {N} to {expected_patches}")

    # 重新整形为空间特征图 [B, patch_h, patch_w, C] -> [B, C, patch_h, patch_w]
    spatial_features = features.view(B, patch_h, patch_w, C).permute(0, 3, 1, 2)
    spatial_features = spatial_features.squeeze(0)  # [C, patch_h, patch_w]

    print(f"Spatial features shape: {spatial_features.shape}")

    # 保存每个channel
    channel_dir = output_dir / f"{image_name}_channels"
    channel_dir.mkdir(exist_ok=True)

    C, fH, fW = spatial_features.shape

    for c in range(C):
        channel_feature = spatial_features[c].cpu().numpy()

        # 归一化到0-255
        if channel_feature.max() > channel_feature.min():
            channel_normalized = (channel_feature - channel_feature.min()) / (channel_feature.max() - channel_feature.min())
        else:
            channel_normalized = np.zeros_like(channel_feature)

        channel_uint8 = (channel_normalized * 255).astype(np.uint8)

        # 保存为灰度图
        channel_path = channel_dir / f"channel_{c:03d}.png"
        cv2.imwrite(str(channel_path), channel_uint8)

        if (c + 1) % 100 == 0:
            print(f"Saved {c + 1}/{C} channels")

    print(f"All channels saved to: {channel_dir}")

    # 创建总览图
    create_overview(spatial_features, output_dir, image_name)


def create_overview(features, output_dir, image_name, max_channels=64):
    """创建特征总览"""
    C = min(features.shape[0], max_channels)
    grid_size = int(np.ceil(np.sqrt(C)))
    
    fig, axes = plt.subplots(grid_size, grid_size, figsize=(20, 20))
    fig.suptitle(f'Feature Maps - First {C} Channels', fontsize=16)
    
    for i in range(grid_size * grid_size):
        row = i // grid_size
        col = i % grid_size
        
        if i < C:
            channel_feature = features[i].cpu().numpy()
            axes[row, col].imshow(channel_feature, cmap='gray')
            axes[row, col].set_title(f'Ch {i}', fontsize=8)
        else:
            axes[row, col].axis('off')
        
        axes[row, col].set_xticks([])
        axes[row, col].set_yticks([])
    
    plt.tight_layout()
    overview_path = output_dir / f"{image_name}_overview.png"
    plt.savefig(overview_path, dpi=150, bbox_inches='tight')
    plt.close()
    
    print(f"Overview saved to: {overview_path}")


def main():
    parser = argparse.ArgumentParser(description='Simple ViT feature visualizer')
    parser.add_argument('--image', type=str, required=True, help='Input image path')
    parser.add_argument('--output', type=str, required=True, help='Output directory')
    parser.add_argument('--device', type=str, default='cuda', choices=['cuda', 'cpu'])
    
    args = parser.parse_args()
    
    if not os.path.exists(args.image):
        print(f"Error: Image {args.image} not found!")
        return
    
    if args.device == 'cuda' and not torch.cuda.is_available():
        args.device = 'cpu'
        print("CUDA not available, using CPU")
    
    print(f"Using device: {args.device}")
    
    try:
        # 创建模型
        print("Creating ViT-Base model...")
        model = create_simple_vit_base()
        model = model.to(args.device)
        model.eval()
        
        print(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")
        
        # 预处理图像
        image_tensor, image_size = preprocess_image(args.image)
        image_tensor = image_tensor.to(args.device)
        
        # 提取特征
        print("Extracting features...")
        with torch.no_grad():
            features, H, W = model(image_tensor)
        
        print(f"Features shape: {features.shape}")
        
        # 可视化
        image_name = Path(args.image).stem
        visualize_features(features, (H, W), args.output, image_name)
        
        print("\n=== Complete ===")
        print(f"Input: {args.image}")
        print(f"Output: {args.output}")
        print(f"Channels: {features.shape[2]}")
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
