#!/usr/bin/env python3
"""
提取DE-ViT第一个传播层特征的完整脚本
专门针对 task=fsod vit=b dataset=coco shot=10
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import torch
import numpy as np
import cv2
import matplotlib.pyplot as plt
from pathlib import Path
import argparse
from detectron2.config import get_cfg
import detectron2.data.transforms as T
import detectron2.data.detection_utils as utils
from tools.train_net import Trainer

class FeatureExtractor:
    def __init__(self):
        self.features = {}
        self.hooks = []
    
    def register_hook(self, model):
        """注册hook到第一个传播层"""
        print("Registering hooks...")
        
        for name, module in model.named_modules():
            # 查找per_cls_cnn (前景传播网络)
            if 'per_cls_cnn' in name and hasattr(module, 'main_layers'):
                print(f"Found propagation network: {name}")
                
                # 获取第一个传播层 (Sequential)
                first_layer = module.main_layers[0]
                print(f"First layer type: {type(first_layer)}")
                
                # 注册hook到整个Sequential的输出
                hook1 = first_layer.register_forward_hook(
                    lambda m, i, o, ln=f"{name}.main_layers[0]": self.save_feature(m, i, o, ln)
                )
                self.hooks.append(hook1)
                
                # 也注册到Conv2d层
                for sub_name, sub_module in first_layer.named_children():
                    if isinstance(sub_module, torch.nn.Conv2d):
                        hook2 = sub_module.register_forward_hook(
                            lambda m, i, o, ln=f"{name}.main_layers[0].{sub_name}": self.save_feature(m, i, o, ln)
                        )
                        self.hooks.append(hook2)
                        print(f"✓ Registered hook: {name}.main_layers[0].{sub_name}")
                        break
                
                break
        
        return len(self.hooks) > 0
    
    def save_feature(self, module, input, output, layer_name):
        """保存特征"""
        if isinstance(output, torch.Tensor):
            self.features[layer_name] = output.detach().cpu()
            print(f"✓ Captured {layer_name}: {output.shape}")
    
    def get_features(self):
        return self.features
    
    def cleanup(self):
        for hook in self.hooks:
            hook.remove()
        self.hooks.clear()

def detect_model_config(model_path):
    """根据模型文件名检测合适的配置"""
    model_name = os.path.basename(model_path).lower()

    # 检测ViT类型
    if 'vitb' in model_name:
        vit_type = 'vitb'
    elif 'vitl' in model_name:
        vit_type = 'vitl'
    elif 'vits' in model_name:
        vit_type = 'vits'
    else:
        vit_type = 'vitb'  # 默认

    # 检测shot数量
    if 'shot5' in model_name:
        shot = 'shot5'
    elif 'shot10' in model_name:
        shot = 'shot10'
    elif 'shot30' in model_name:
        shot = 'shot30'
    else:
        shot = 'shot10'  # 默认

    config_file = f'configs/few-shot/{vit_type}_{shot}.yaml'
    print(f"Auto-detected config: {config_file}")
    return config_file

def setup_config_for_fsod(model_path, device='cuda', config_file=None):
    """为Few-Shot目标检测设置配置"""
    config = get_cfg()

    # 自动检测或使用指定的配置文件
    if config_file is None:
        config_file = detect_model_config(model_path)

    if not os.path.exists(config_file):
        print(f"⚠️  Config file not found: {config_file}")
        print("Using default: configs/few-shot/vitb_shot10.yaml")
        config_file = 'configs/few-shot/vitb_shot10.yaml'

    config.merge_from_file(config_file)
    print(f"✓ Loaded config: {config_file}")

    # 基本设置
    config.MODEL.DEVICE = device
    config.MODEL.WEIGHTS = model_path
    config.MODEL.MASK_ON = True

    # Few-Shot特定设置
    config.MODEL.ROI_HEADS.NUM_CLASSES = 80  # COCO类别

    # 确保RPN配置存在
    if not hasattr(config.DE, 'OFFLINE_RPN_CONFIG') or not config.DE.OFFLINE_RPN_CONFIG:
        config.DE.OFFLINE_RPN_CONFIG = "configs/RPN/mask_rcnn_R_50_FPN_1x.yaml"
        print(f"Set OFFLINE_RPN_CONFIG to: {config.DE.OFFLINE_RPN_CONFIG}")

    config.freeze()
    return config

def load_fsod_model(config, model_path, device='cuda'):
    """加载Few-Shot目标检测模型"""
    print(f"Loading FSOD model from: {model_path}")

    if not os.path.exists(model_path):
        raise FileNotFoundError(f"Model file not found: {model_path}")

    model = Trainer.build_model(config).to(device)

    # 加载权重
    checkpoint = torch.load(model_path, map_location=device)
    if 'model' in checkpoint:
        state_dict = checkpoint['model']
    else:
        state_dict = checkpoint

    # 尝试加载权重，处理不匹配的情况
    try:
        model.load_state_dict(state_dict, strict=True)
        print("✓ Model weights loaded with strict=True")
    except RuntimeError as e:
        print(f"⚠️  Strict loading failed: {str(e)[:200]}...")
        print("Trying flexible loading...")

        # 获取模型的参数名
        model_keys = set(model.state_dict().keys())
        checkpoint_keys = set(state_dict.keys())

        # 找到匹配的参数
        matching_keys = model_keys.intersection(checkpoint_keys)
        missing_keys = model_keys - checkpoint_keys
        unexpected_keys = checkpoint_keys - model_keys

        print(f"  Matching keys: {len(matching_keys)}")
        print(f"  Missing keys: {len(missing_keys)}")
        print(f"  Unexpected keys: {len(unexpected_keys)}")

        if missing_keys:
            print("  Missing keys (first 10):")
            for key in list(missing_keys)[:10]:
                print(f"    {key}")

        # 只加载匹配的权重
        filtered_state_dict = {k: v for k, v in state_dict.items() if k in model_keys}
        model.load_state_dict(filtered_state_dict, strict=False)
        print("✓ Model weights loaded with flexible loading")

    model.eval()
    print("✓ FSOD model loaded successfully!")
    return model

def preprocess_image(image_path, config):
    """预处理图像"""
    print(f"Processing image: {image_path}")
    
    if not os.path.exists(image_path):
        raise FileNotFoundError(f"Image file not found: {image_path}")
    
    image = utils.read_image(image_path, format="RGB")
    height, width = image.shape[0], image.shape[1]
    print(f"Original image size: {width}x{height}")
    
    # 构建数据增强
    augs = utils.build_augmentation(config, False)
    augmentations = T.AugmentationList(augs)
    
    # 应用增强
    aug_input = T.AugInput(image)
    augmentations(aug_input)
    
    dataset_dict = {
        "height": height,
        "width": width,
        "image": torch.as_tensor(np.ascontiguousarray(aug_input.image.transpose(2, 0, 1)))
    }
    
    return [dataset_dict], image

def visualize_conv_features(features, layer_name, output_dir, max_channels=16):
    """可视化卷积特征"""
    print(f"Visualizing {layer_name}: {features.shape}")
    
    if len(features.shape) != 4:
        print(f"Unexpected shape: {features.shape}")
        return
    
    batch_size, num_channels, height, width = features.shape
    features = features[0]  # 第一个batch
    num_channels = min(num_channels, max_channels)
    
    # 创建输出目录
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    # 网格可视化
    cols = 4
    rows = (num_channels + cols - 1) // cols
    
    fig, axes = plt.subplots(rows, cols, figsize=(cols*3, rows*3))
    if rows == 1:
        axes = axes.reshape(1, -1)
    
    for i in range(num_channels):
        row = i // cols
        col = i % cols
        
        feature_map = features[i].numpy()
        
        # 归一化
        if feature_map.max() > feature_map.min():
            feature_map = (feature_map - feature_map.min()) / (feature_map.max() - feature_map.min())
        
        im = axes[row, col].imshow(feature_map, cmap='viridis')
        axes[row, col].set_title(f'Channel {i}')
        axes[row, col].axis('off')
        plt.colorbar(im, ax=axes[row, col], fraction=0.046, pad=0.04)
    
    # 隐藏多余子图
    for i in range(num_channels, rows * cols):
        row = i // cols
        col = i % cols
        axes[row, col].axis('off')
    
    plt.suptitle(f'First Propagation Layer Features\n{layer_name}', fontsize=14)
    plt.tight_layout()
    
    # 保存
    safe_name = layer_name.replace('.', '_').replace('/', '_')
    output_file = output_path / f"{safe_name}_features.png"
    plt.savefig(output_file, dpi=150, bbox_inches='tight')
    plt.close()
    
    print(f"✓ Saved: {output_file}")
    
    # 保存单独通道
    channel_dir = output_path / f"{safe_name}_channels"
    channel_dir.mkdir(exist_ok=True)
    
    for i in range(num_channels):
        feature_map = features[i].numpy()
        
        if feature_map.max() > feature_map.min():
            feature_map = (feature_map - feature_map.min()) / (feature_map.max() - feature_map.min())
        feature_map = (feature_map * 255).astype(np.uint8)
        
        channel_file = channel_dir / f"channel_{i:03d}.png"
        cv2.imwrite(str(channel_file), feature_map)
    
    print(f"✓ Saved channels: {channel_dir}")

def main():
    parser = argparse.ArgumentParser(description='Extract DE-ViT first propagation layer features')
    parser.add_argument('--image', type=str, required=True, 
                       help='Path to test image')
    parser.add_argument('--model', type=str, required=True,
                       help='Path to trained FSOD model weights')
    parser.add_argument('--output', type=str, default='propagation_features',
                       help='Output directory')
    parser.add_argument('--device', type=str, default='cuda',
                       choices=['cuda', 'cpu'], help='Device')
    parser.add_argument('--max-channels', type=int, default=16,
                       help='Max channels to visualize')
    
    args = parser.parse_args()
    
    # 检查设备
    if args.device == 'cuda' and not torch.cuda.is_available():
        print("CUDA not available, using CPU")
        args.device = 'cpu'
    
    extractor = FeatureExtractor()
    
    try:
        print("DE-ViT First Propagation Layer Feature Extraction")
        print("=" * 55)
        print(f"Task: Few-Shot Object Detection (FSOD)")
        print(f"Model: ViT-Base, 10-shot, COCO dataset")
        print(f"Image: {args.image}")
        print(f"Model: {args.model}")
        print(f"Device: {args.device}")
        print()
        
        # 1. 设置配置 (自动检测配置文件)
        config = setup_config_for_fsod(args.model, args.device)
        print("✓ Configuration loaded")
        
        # 2. 加载模型
        model = load_fsod_model(config, args.model, args.device)
        
        # 3. 注册hooks
        if not extractor.register_hook(model):
            print("❌ Failed to register hooks!")
            return 1
        
        # 4. 预处理图像
        batched_inputs, original_image = preprocess_image(args.image, config)
        
        # 5. 运行推理
        print("\nRunning inference...")
        for item in batched_inputs:
            item["image"] = item["image"].to(args.device)
        
        with torch.no_grad():
            outputs = model(batched_inputs)
        
        print("✓ Inference completed")
        
        # 6. 获取特征
        features = extractor.get_features()
        
        if features:
            print(f"\n✓ Captured features from {len(features)} layer(s):")
            for name, feat in features.items():
                print(f"  {name}: {feat.shape}")
            
            # 7. 可视化
            for name, feat in features.items():
                visualize_conv_features(feat, name, args.output, args.max_channels)
            
            print(f"\n🎉 Feature extraction and visualization completed!")
            print(f"Output directory: {args.output}")
        else:
            print("\n❌ No features captured!")
        
        return 0
        
    except Exception as e:
        print(f"\n💥 Error: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    finally:
        extractor.cleanup()

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
