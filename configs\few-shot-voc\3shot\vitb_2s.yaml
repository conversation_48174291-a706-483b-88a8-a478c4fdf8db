_BASE_: "../../Base-RCNN-C4.yaml"
DE:
  CLASS_PROTOTYPES: "weights/initial/few-shot-voc/prototypes/pascal_voc_train_split_2.vitb14.bbox.p10.sk.pkl,weights/initial/few-shot-voc/prototypes/voc_2007_trainval_novel2_3shot.vitb14.aug.bbox.p10.sk.pkl"
  BG_PROTOTYPES: "weights/initial/background/background_prototypes.vitb14.pth"
  BG_CLS_LOSS_WEIGHT: 0.2
  TOPK: 10

MODEL:
  META_ARCHITECTURE: "OpenSetDetectorWithExamples"
  BACKBONE:
    NAME: "build_dino_v2_vit"
    TYPE: "base" # base, small
  WEIGHTS: "" 
  MASK_ON: False
  RPN:
    HEAD_NAME: StandardRPNHead
    IN_FEATURES: ["res4"]
  ROI_HEADS:
    SCORE_THRESH_TEST: 0.001
  ROI_BOX_HEAD:
    NAME: ""
    NUM_FC: 0
    POOLER_RESOLUTION: 7
    CLS_AGNOSTIC_BBOX_REG: True
  PIXEL_MEAN: [0.48145466, 0.4578275, 0.40821073]
  PIXEL_STD: [0.26862954, 0.26130258, 0.27577711]
INPUT:
  MIN_SIZE_TRAIN: (640, 672, 704, 736, 768, 800)
DATASETS:
  TRAIN: ("pascal_voc_train_split_2",)
  TEST: ("voc_2007_test_all2",)
TEST:
  EVAL_PERIOD: 5000
SOLVER:
  IMS_PER_BATCH: 16
  BASE_LR: 0.002
  STEPS: (12000, 16000)
  MAX_ITER: 20000
  WARMUP_ITERS: 5000
  CHECKPOINT_PERIOD: 5000


INPUT:
  MIN_SIZE_TRAIN_SAMPLING: choice
  MIN_SIZE_TRAIN: (640, 672, 704, 736, 768, 800)
  MAX_SIZE_TRAIN: 1333
  MIN_SIZE_TEST: 800
  MAX_SIZE_TEST: 1333
  FORMAT: "RGB"