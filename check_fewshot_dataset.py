import os
from collections import defaultdict

# 确保在尝试访问数据集之前先注册它们
# 导入 fewshot.py 会触发 `register_all_coco()` 函数
print("正在注册COCO小样本数据集...")
try:
    import lib.data.fewshot 
except ImportError:
    print("错误: 无法导入 'lib.data.fewshot'。")
    print("请确保您在项目的根目录下运行此脚本, 并且您的PYTHONPATH设置正确。")
    exit(1)
print("数据集注册完毕。")

from detectron2.data import DatasetCatalog, MetadataCatalog

def main():
    """
    加载并检查一个已注册的小样本数据集，打印出每个类别中的样本。
    """
    # 您可以在这里更改数据集名称来检查其他数据集
    # 例如: "fs_coco_trainval_novel_5shot", "fs_coco_trainval_novel_30shot"
    # dataset_name = "fs_coco_trainval_novel_10shot"
    dataset_name = "fs_coco_trainval_base" # <-- 我们现在检查基类数据集
    print(f"\n正在加载数据集: {dataset_name}")

    # 从 Detectron2 的目录中获取已注册的数据集
    # The call to `()` executes the lambda function registered for this dataset.
    try:
        dataset_dicts = DatasetCatalog.get(dataset_name)
    except KeyError:
        print(f"错误: 数据集 '{dataset_name}' 未找到。")
        print("请检查数据集名称是否正确, 以及它是否已在 'lib/data/fewshot.py' 中注册。")
        return

    print(f"{dataset_name} 中的样本总数: {len(dataset_dicts)}")

    samples_by_class = defaultdict(list)

    # 获取数据集的元数据，用于将类别ID映射到可读的类名
    metadata = MetadataCatalog.get(dataset_name)
    thing_classes = metadata.thing_classes
    
    for d in dataset_dicts:
        # 每个字典 `d` 是一个样本（通常是一张图片），它可能包含多个标注。
        # 对于小样本数据集，每个图像通常只有一个目标类别的标注对象。
        if not d.get('annotations'):
            continue
        
        # 从第一个标注中获取类名
        category_id = d['annotations'][0]['category_id']
        class_name = thing_classes[category_id]
        
        # 存储文件名，避免重复（以防一张图片有多个相同类别的标注）
        if d['file_name'] not in samples_by_class[class_name]:
            samples_by_class[class_name].append(d['file_name'])

    print("\n--- {} 中每个类别的样本 ---".format(dataset_name))
    for class_name, files in sorted(samples_by_class.items()):
        print(f"\n类别: '{class_name}' ({len(files)} 个样本)")
        for file_name in files:
            # 文件路径通常是完整的，我们只显示基本的文件名以提高可读性
            print(f"  - {os.path.basename(file_name)}")
    
    print("\n注意: 文件路径是相对于您的数据集目录的 (例如, 'datasets/coco/trainval2014/')。")
    print("要运行此脚本, 请在项目根目录下执行 `python check_fewshot_dataset.py`。")

if __name__ == "__main__":
    main() 