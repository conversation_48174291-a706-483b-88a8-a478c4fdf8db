#!/usr/bin/env python3
"""
调试模型权重加载问题
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import torch
import argparse
from pathlib import Path

def analyze_checkpoint(checkpoint_path):
    """分析检查点文件"""
    print(f"Analyzing checkpoint: {checkpoint_path}")
    
    if not os.path.exists(checkpoint_path):
        print(f"❌ File not found: {checkpoint_path}")
        return
    
    try:
        checkpoint = torch.load(checkpoint_path, map_location='cpu')
        print(f"✓ Checkpoint loaded successfully")
        
        # 检查检查点结构
        print(f"\nCheckpoint keys: {list(checkpoint.keys())}")
        
        # 获取模型权重
        if 'model' in checkpoint:
            state_dict = checkpoint['model']
            print("✓ Found 'model' key in checkpoint")
        elif 'state_dict' in checkpoint:
            state_dict = checkpoint['state_dict']
            print("✓ Found 'state_dict' key in checkpoint")
        else:
            state_dict = checkpoint
            print("✓ Using checkpoint as state_dict directly")
        
        # 分析权重键
        all_keys = list(state_dict.keys())
        print(f"\nTotal parameters: {len(all_keys)}")
        
        # 按模块分组
        modules = {}
        for key in all_keys:
            module_name = key.split('.')[0]
            if module_name not in modules:
                modules[module_name] = []
            modules[module_name].append(key)
        
        print(f"\nModules found:")
        for module, keys in modules.items():
            print(f"  {module}: {len(keys)} parameters")
        
        # 检查是否有offline_backbone
        offline_keys = [k for k in all_keys if 'offline_backbone' in k]
        if offline_keys:
            print(f"\n✓ Found offline_backbone parameters: {len(offline_keys)}")
            print("First 5 offline_backbone keys:")
            for key in offline_keys[:5]:
                print(f"  {key}")
        else:
            print(f"\n❌ No offline_backbone parameters found")
        
        # 检查传播层相关参数
        prop_keys = [k for k in all_keys if 'per_cls_cnn' in k or 'propagat' in k.lower()]
        if prop_keys:
            print(f"\n✓ Found propagation layer parameters: {len(prop_keys)}")
            print("Propagation layer keys:")
            for key in prop_keys:
                print(f"  {key}")
        else:
            print(f"\n⚠️  No propagation layer parameters found")
        
        # 其他有用信息
        if 'optimizer' in checkpoint:
            print(f"\n✓ Optimizer state found")
        if 'scheduler' in checkpoint:
            print(f"✓ Scheduler state found")
        if 'epoch' in checkpoint:
            print(f"✓ Epoch: {checkpoint['epoch']}")
        if 'iteration' in checkpoint:
            print(f"✓ Iteration: {checkpoint['iteration']}")
            
    except Exception as e:
        print(f"❌ Error loading checkpoint: {e}")
        import traceback
        traceback.print_exc()

def compare_with_model(checkpoint_path, config_file=None):
    """比较检查点与模型架构"""
    print(f"\n{'='*50}")
    print("Comparing checkpoint with model architecture")
    print(f"{'='*50}")
    
    try:
        from detectron2.config import get_cfg
        from tools.train_net import Trainer
        
        # 设置配置
        config = get_cfg()
        if config_file is None:
            config_file = 'configs/few-shot/vitb_shot10.yaml'
        
        config.merge_from_file(config_file)
        config.MODEL.DEVICE = 'cpu'
        config.MODEL.WEIGHTS = ''
        
        # 修复RPN配置
        if not hasattr(config.DE, 'OFFLINE_RPN_CONFIG') or not config.DE.OFFLINE_RPN_CONFIG:
            config.DE.OFFLINE_RPN_CONFIG = "configs/RPN/mask_rcnn_R_50_FPN_1x.yaml"
        
        config.freeze()
        
        # 创建模型
        print(f"Creating model with config: {config_file}")
        model = Trainer.build_model(config)
        model_keys = set(model.state_dict().keys())
        print(f"✓ Model created with {len(model_keys)} parameters")
        
        # 加载检查点
        checkpoint = torch.load(checkpoint_path, map_location='cpu')
        if 'model' in checkpoint:
            checkpoint_keys = set(checkpoint['model'].keys())
        else:
            checkpoint_keys = set(checkpoint.keys())
        
        print(f"✓ Checkpoint has {len(checkpoint_keys)} parameters")
        
        # 比较
        matching = model_keys.intersection(checkpoint_keys)
        missing = model_keys - checkpoint_keys
        unexpected = checkpoint_keys - model_keys
        
        print(f"\nComparison results:")
        print(f"  Matching parameters: {len(matching)}")
        print(f"  Missing in checkpoint: {len(missing)}")
        print(f"  Unexpected in checkpoint: {len(unexpected)}")
        
        if missing:
            print(f"\nMissing parameters (first 10):")
            for key in list(missing)[:10]:
                print(f"  {key}")
        
        if unexpected:
            print(f"\nUnexpected parameters (first 10):")
            for key in list(unexpected)[:10]:
                print(f"  {key}")
        
        # 检查关键模块
        print(f"\nKey module analysis:")
        
        # Backbone
        backbone_model = [k for k in model_keys if 'backbone' in k]
        backbone_checkpoint = [k for k in checkpoint_keys if 'backbone' in k]
        print(f"  Backbone - Model: {len(backbone_model)}, Checkpoint: {len(backbone_checkpoint)}")
        
        # Offline backbone
        offline_model = [k for k in model_keys if 'offline_backbone' in k]
        offline_checkpoint = [k for k in checkpoint_keys if 'offline_backbone' in k]
        print(f"  Offline Backbone - Model: {len(offline_model)}, Checkpoint: {len(offline_checkpoint)}")
        
        # Propagation layers
        prop_model = [k for k in model_keys if 'per_cls_cnn' in k]
        prop_checkpoint = [k for k in checkpoint_keys if 'per_cls_cnn' in k]
        print(f"  Propagation - Model: {len(prop_model)}, Checkpoint: {len(prop_checkpoint)}")
        
        return len(matching) / len(model_keys) > 0.5  # 如果超过50%匹配就认为兼容
        
    except Exception as e:
        print(f"❌ Error in model comparison: {e}")
        import traceback
        traceback.print_exc()
        return False

def suggest_solutions(checkpoint_path):
    """建议解决方案"""
    print(f"\n{'='*50}")
    print("Suggested Solutions")
    print(f"{'='*50}")
    
    print("1. 尝试不同的配置文件:")
    configs = [
        'configs/few-shot/vitb_shot10.yaml',
        'configs/few-shot/vitb_shot5.yaml',
        'configs/few-shot/vitb_shot30.yaml',
        'configs/few-shot/vitl_shot10.yaml',
        'configs/few-shot/vits_shot10.yaml'
    ]
    
    for config in configs:
        if os.path.exists(config):
            print(f"   python extract_propagation_features.py --model {checkpoint_path} --config {config}")
    
    print(f"\n2. 使用灵活加载模式 (已在脚本中实现):")
    print(f"   脚本会自动尝试 strict=False 加载")
    
    print(f"\n3. 检查模型是否为正确的DE-ViT模型:")
    print(f"   确保模型是用DE-ViT框架训练的Few-Shot模型")
    
    print(f"\n4. 如果是预训练模型，可能需要:")
    print(f"   - 使用对应的预训练配置文件")
    print(f"   - 或者进行模型转换")

def main():
    parser = argparse.ArgumentParser(description='Debug model weight loading issues')
    parser.add_argument('--model', type=str, required=True, help='Path to model checkpoint')
    parser.add_argument('--config', type=str, help='Config file to compare with')
    parser.add_argument('--compare', action='store_true', help='Compare with model architecture')
    
    args = parser.parse_args()
    
    print("DE-ViT Model Weight Debugger")
    print("=" * 40)
    
    # 分析检查点
    analyze_checkpoint(args.model)
    
    # 可选：与模型架构比较
    if args.compare:
        compatible = compare_with_model(args.model, args.config)
        if compatible:
            print("\n✅ Checkpoint appears compatible with model architecture")
        else:
            print("\n⚠️  Checkpoint may have compatibility issues")
    
    # 建议解决方案
    suggest_solutions(args.model)

if __name__ == "__main__":
    main()
