#!/usr/bin/env python3
"""
使用修改后的模型可视化第一个传播层特征
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import torch
import numpy as np
import cv2
import matplotlib.pyplot as plt
from pathlib import Path
import argparse
from detectron2.config import get_cfg
import detectron2.data.transforms as T
import detectron2.data.detection_utils as utils
from tools.train_net import Trainer

def setup_config(config_file, model_path, device='cuda'):
    """设置配置"""
    config = get_cfg()
    config.merge_from_file(config_file)
    
    config.MODEL.DEVICE = device
    config.MODEL.WEIGHTS = model_path
    config.MODEL.MASK_ON = True
    config.MODEL.ROI_HEADS.NUM_CLASSES = 80
    
    config.freeze()
    return config

def load_model_with_feature_saving(config, model_path, device='cuda'):
    """加载模型并启用特征保存"""
    print(f"Loading model from: {model_path}")
    
    model = Trainer.build_model(config).to(device)
    checkpoint = torch.load(model_path, map_location=device)
    model.load_state_dict(checkpoint['model'])
    model.eval()
    
    # 启用第一层特征保存
    for name, module in model.named_modules():
        if hasattr(module, 'main_layers') and 'per_cls_cnn' in name:
            module.save_first_layer_features = True
            print(f"Enabled feature saving for: {name}")
            break
    
    print("✓ Model loaded with feature saving enabled!")
    return model

def preprocess_image(image_path, config):
    """预处理图像"""
    print(f"Processing image: {image_path}")
    
    image = utils.read_image(image_path, format="RGB")
    height, width = image.shape[0], image.shape[1]
    
    augs = utils.build_augmentation(config, False)
    augmentations = T.AugmentationList(augs)
    
    aug_input = T.AugInput(image)
    augmentations(aug_input)
    
    dataset_dict = {
        "height": height,
        "width": width,
        "image": torch.as_tensor(np.ascontiguousarray(aug_input.image.transpose(2, 0, 1)))
    }
    
    return [dataset_dict], image

def run_inference_and_extract_features(model, batched_inputs, device='cuda'):
    """运行推理并提取特征"""
    print("Running inference...")
    
    for item in batched_inputs:
        item["image"] = item["image"].to(device)
    
    with torch.no_grad():
        outputs = model(batched_inputs)
    
    # 提取保存的特征
    extracted_features = []
    for name, module in model.named_modules():
        if hasattr(module, 'first_layer_features'):
            extracted_features.append({
                'layer_name': name,
                'features': module.first_layer_features
            })
            print(f"✓ Extracted features from {name}: {module.first_layer_features.shape}")
    
    return outputs, extracted_features

def visualize_features(features, layer_name, output_dir, max_channels=16):
    """可视化特征"""
    print(f"Visualizing features from {layer_name}")
    
    if len(features.shape) != 4:
        print(f"Unexpected feature shape: {features.shape}")
        return
    
    batch_size, num_channels, height, width = features.shape
    features = features[0]  # 取第一个batch
    num_channels = min(num_channels, max_channels)
    
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    # 创建网格可视化
    cols = 4
    rows = (num_channels + cols - 1) // cols
    
    fig, axes = plt.subplots(rows, cols, figsize=(cols*3, rows*3))
    if rows == 1:
        axes = axes.reshape(1, -1)
    
    for i in range(num_channels):
        row = i // cols
        col = i % cols
        
        feature_map = features[i].numpy()
        
        # 归一化
        if feature_map.max() > feature_map.min():
            feature_map = (feature_map - feature_map.min()) / (feature_map.max() - feature_map.min())
        
        im = axes[row, col].imshow(feature_map, cmap='viridis')
        axes[row, col].set_title(f'Channel {i}')
        axes[row, col].axis('off')
        plt.colorbar(im, ax=axes[row, col], fraction=0.046, pad=0.04)
    
    # 隐藏多余的子图
    for i in range(num_channels, rows * cols):
        row = i // cols
        col = i % cols
        axes[row, col].axis('off')
    
    plt.tight_layout()
    
    # 保存
    safe_name = layer_name.replace('.', '_').replace('/', '_')
    output_file = output_path / f"{safe_name}_first_layer_features.png"
    plt.savefig(output_file, dpi=150, bbox_inches='tight')
    plt.close()
    
    print(f"✓ Saved visualization: {output_file}")
    
    # 保存单独通道
    channel_dir = output_path / f"{safe_name}_channels"
    channel_dir.mkdir(exist_ok=True)
    
    for i in range(num_channels):
        feature_map = features[i].numpy()
        
        if feature_map.max() > feature_map.min():
            feature_map = (feature_map - feature_map.min()) / (feature_map.max() - feature_map.min())
        feature_map = (feature_map * 255).astype(np.uint8)
        
        channel_file = channel_dir / f"channel_{i:03d}.png"
        cv2.imwrite(str(channel_file), feature_map)
    
    print(f"✓ Saved individual channels: {channel_dir}")

def main():
    parser = argparse.ArgumentParser(description='Visualize first propagation layer features')
    parser.add_argument('--image', type=str, required=True, help='Path to input image')
    parser.add_argument('--config', type=str, 
                       default='configs/few-shot/vitb_shot10.yaml',
                       help='Path to config file')
    parser.add_argument('--model', type=str,
                       help='Path to model weights')
    parser.add_argument('--output', type=str, default='first_layer_features',
                       help='Output directory')
    parser.add_argument('--device', type=str, default='cuda',
                       choices=['cuda', 'cpu'], help='Device to use')
    parser.add_argument('--max-channels', type=int, default=16,
                       help='Maximum channels to visualize')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.image):
        print(f"Error: Image {args.image} not found!")
        return 1
    
    # 自动检测模型
    if args.model is None:
        import glob
        patterns = [
            "weights/trained/few-shot/coco/vitb_shot10_*.pth",
            "weights/trained/few-shot/vitb_shot10_*.pth",
            "weights/**/vitb_shot10*.pth"
        ]
        
        model_files = []
        for pattern in patterns:
            model_files.extend(glob.glob(pattern, recursive=True))
        
        if not model_files:
            print("Error: No model weights found. Please specify --model")
            return 1
        
        args.model = model_files[0]
        print(f"Auto-detected model: {args.model}")
    
    if args.device == 'cuda' and not torch.cuda.is_available():
        print("CUDA not available, using CPU")
        args.device = 'cpu'
    
    try:
        # 1. 设置配置
        config = setup_config(args.config, args.model, args.device)
        
        # 2. 加载模型
        model = load_model_with_feature_saving(config, args.model, args.device)
        
        # 3. 预处理图像
        batched_inputs, original_image = preprocess_image(args.image, config)
        
        # 4. 运行推理
        outputs, extracted_features = run_inference_and_extract_features(model, batched_inputs, args.device)
        
        # 5. 可视化
        if extracted_features:
            for feature_data in extracted_features:
                visualize_features(
                    feature_data['features'],
                    feature_data['layer_name'],
                    args.output,
                    args.max_channels
                )
            
            print(f"\n✓ Feature visualization completed!")
            print(f"Output directory: {args.output}")
        else:
            print("\n✗ No features extracted. Check model modification.")
        
        return 0
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
