import os
import numpy as np
from PIL import Image
import random


def generate_thresholded_images(num_images=60, width=57, height=86, output_folder='generated_thresholded_images'):
    """
    根据不同规则生成带有阈值处理的随机灰度图像，并保存到指定文件夹。

    参数:
    num_images (int): 要生成的图像数量。
    width (int): 图像的宽度（矩阵的列数）。
    height (int): 图像的高度（矩阵的行数）。
    output_folder (str): 保存生成图像的文件夹名称。
    """
    # 1. 检查并创建输出文件夹
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)
        print(f"文件夹 '{output_folder}' 已创建。")

    # 2. 定义每种规则的参数：(随机数上限, 置零阈值)
    options = [
        (1.0, 0.5),  # 30% 概率: 范围[0, 1.0], 低于0.5的置零
        (0.6, 0.25),  # 30% 概率: 范围[0, 0.6], 低于0.25的置零
        (0.4, 0.15),  # 20% 概率: 范围[0, 0.4], 低于0.15的置零
        (0.2, 0.1)  # 20% 概率: 范围[0, 0.2], 低于0.1的置零
    ]
    # 每种规则被选中的概率
    probabilities = [0.3, 0.3, 0.2, 0.2]

    # 3. 循环生成和保存60个图像
    print("开始生成图像...")
    for i in range(num_images):
        # 根据概率随机选择一个规则（包含上限和阈值）
        # random.choices返回一个列表，所以我们取第一个元素[0]
        max_val, threshold = random.choices(options, probabilities)[0]

        # 生成在[0, max_val)范围内的随机二维矩阵
        random_matrix = np.random.rand(height, width) * max_val

        # 新增步骤：应用阈值处理
        # 使用Numpy的布尔索引，高效地将所有低于阈值的元素置为0
        random_matrix[random_matrix < threshold] = 0

        # 将矩阵数值范围从[0, max_val]映射到[0, 255]的整数灰度值
        # astype('uint8') 将浮点数转换为8位无符号整数
        grayscale_array = (random_matrix * 255).astype(np.uint8)

        # 从numpy数组创建灰度图像 ('L' 模式代表灰度)
        image = Image.fromarray(grayscale_array, mode='L')

        # 定义保存路径和文件名
        file_path = os.path.join(output_folder, f'image_thresholded_{i + 1}.png')

        # 保存图像
        image.save(file_path)

    print(f"成功！ {num_images} 张带阈值处理的灰度图像已全部生成并保存于 '{output_folder}' 文件夹中。")


# --- 主程序入口 ---
if __name__ == '__main__':
    # 定义图像参数
    NUM_IMAGES_TO_GENERATE = 60
    IMAGE_WIDTH = 86
    IMAGE_HEIGHT = 57
    OUTPUT_FOLDER_NAME = 'D:\coding\devit\output_attentionmap'

    # 调用函数开始生成
    generate_thresholded_images(
        num_images=NUM_IMAGES_TO_GENERATE,
        width=IMAGE_WIDTH,
        height=IMAGE_HEIGHT,
        output_folder=OUTPUT_FOLDER_NAME
    )