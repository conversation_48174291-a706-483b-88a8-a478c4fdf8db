class A():
    def __init__(self):
        self.a = 1

    def test(self):
        self.mytest()
        self.mytest1()

    def mytest1(self):
        print("A1")
        # return self.a
    def mytest(self):
        print("A")
        # return self.a

class B(A):
    # def __init__(self):
    #     # super().__init__()
    #     self.b = 2
    def test(self):
        super().test()
        # return self.b
    def mytest(self):
        print("B")
        # return self.b
a = B()
a.test()
print(a.a)