#!/usr/bin/env python3
"""
自适应池化脚本使用示例
"""

import os
import subprocess
import sys
from pathlib import Path

def example_1_single_image():
    """示例1: 处理单张图像"""
    print("=== 示例1: 处理单张图像 ===")
    
    # 假设您有一张图像
    input_image = "path/to/your/image.png"
    output_image = "pooled_image_7x7.png"
    
    cmd = [
        "python", "adaptive_pool_grayscale.py",
        "--input", input_image,
        "--output", output_image,
        "--verbose"
    ]
    
    print("命令:")
    print(" ".join(cmd))
    print()
    print("说明:")
    print("- 将单张灰度图像池化到7x7大小")
    print("- --verbose 显示详细处理信息")
    print()

def example_2_batch_directory():
    """示例2: 批量处理目录"""
    print("=== 示例2: 批量处理目录 ===")
    
    input_dir = "input_grayscale_images"
    output_dir = "output_7x7_images"
    
    cmd = [
        "python", "adaptive_pool_grayscale.py",
        "--input", input_dir,
        "--output", output_dir
    ]
    
    print("命令:")
    print(" ".join(cmd))
    print()
    print("说明:")
    print("- 处理整个目录中的所有图像")
    print("- 自动识别常见图像格式 (.jpg, .png, .bmp等)")
    print("- 保持原始文件名")
    print()

def example_3_specific_extensions():
    """示例3: 指定文件扩展名"""
    print("=== 示例3: 指定文件扩展名 ===")
    
    input_dir = "channel_images"
    output_dir = "pooled_channels"
    
    cmd = [
        "python", "adaptive_pool_grayscale.py",
        "--input", input_dir,
        "--output", output_dir,
        "--extensions", ".png", ".jpg",
        "--verbose"
    ]
    
    print("命令:")
    print(" ".join(cmd))
    print()
    print("说明:")
    print("- 只处理指定扩展名的文件")
    print("- 适用于特定格式的图像集合")
    print()

def example_4_feature_channels():
    """示例4: 处理特征通道图像"""
    print("=== 示例4: 处理特征通道图像 ===")
    
    # 假设您有从backbone提取的特征通道图像
    input_dir = "feature_visualizations/image_name_channels"
    output_dir = "pooled_features_7x7"
    
    cmd = [
        "python", "adaptive_pool_grayscale.py",
        "--input", input_dir,
        "--output", output_dir,
        "--extensions", ".png"
    ]
    
    print("命令:")
    print(" ".join(cmd))
    print()
    print("说明:")
    print("- 专门用于处理特征可视化生成的通道图像")
    print("- 将每个通道从任意尺寸池化到7x7")
    print("- 适用于ROI特征提取等应用")
    print()

def create_demo_script():
    """创建演示脚本"""
    demo_script = '''#!/usr/bin/env python3
"""
自适应池化演示脚本
"""

import os
import cv2
import numpy as np
from pathlib import Path
import subprocess

def create_demo_images():
    """创建演示用的图像"""
    demo_dir = Path("demo_input")
    demo_dir.mkdir(exist_ok=True)
    
    # 创建不同尺寸的灰度图像
    sizes = [(64, 64), (128, 96), (256, 192), (100, 150)]
    
    for i, (w, h) in enumerate(sizes):
        # 创建带图案的图像
        img = np.zeros((h, w), dtype=np.uint8)
        
        # 添加渐变
        for y in range(h):
            for x in range(w):
                img[y, x] = int((x + y) * 255 / (w + h))
        
        # 添加形状
        cv2.rectangle(img, (w//4, h//4), (3*w//4, 3*h//4), 128, 2)
        cv2.circle(img, (w//2, h//2), min(w, h)//8, 255, -1)
        
        # 保存
        filename = f"demo_image_{i+1}_{w}x{h}.png"
        cv2.imwrite(str(demo_dir / filename), img)
        print(f"Created: {filename}")
    
    return demo_dir

def run_demo():
    """运行演示"""
    print("创建演示图像...")
    input_dir = create_demo_images()
    output_dir = "demo_output_7x7"
    
    print("\\n运行自适应池化...")
    cmd = [
        "python", "adaptive_pool_grayscale.py",
        "--input", str(input_dir),
        "--output", output_dir,
        "--verbose"
    ]
    
    try:
        result = subprocess.run(cmd, check=True)
        print("\\n✓ 演示完成!")
        print(f"输入目录: {input_dir}")
        print(f"输出目录: {output_dir}")
        
        # 显示结果
        output_path = Path(output_dir)
        if output_path.exists():
            files = list(output_path.glob("*.png"))
            print(f"生成了 {len(files)} 个7x7图像")
            
            # 验证尺寸
            for file in files[:3]:  # 检查前3个
                img = cv2.imread(str(file), cv2.IMREAD_GRAYSCALE)
                print(f"  {file.name}: {img.shape}")
        
    except subprocess.CalledProcessError as e:
        print(f"演示失败: {e}")

if __name__ == "__main__":
    run_demo()
'''
    
    with open("demo_adaptive_pool.py", "w", encoding="utf-8") as f:
        f.write(demo_script)
    
    print("=== 创建演示脚本 ===")
    print("已创建: demo_adaptive_pool.py")
    print()
    print("运行演示:")
    print("python demo_adaptive_pool.py")
    print()

def show_help():
    """显示帮助信息"""
    print("=== 完整参数说明 ===")
    print()
    print("python adaptive_pool_grayscale.py [参数]")
    print()
    print("必需参数:")
    print("  --input, -i    输入路径 (文件或目录)")
    print("  --output, -o   输出路径 (文件或目录)")
    print()
    print("可选参数:")
    print("  --extensions   支持的文件扩展名 (默认: .jpg .jpeg .png .bmp .tiff .tif)")
    print("  --verbose, -v  显示详细处理信息")
    print("  --help, -h     显示帮助信息")
    print()
    print("输出格式:")
    print("- 所有图像都会被池化到7x7像素大小")
    print("- 保持原始文件名和格式")
    print("- 使用自适应平均池化保持图像特征")
    print()

def main():
    print("自适应池化脚本使用指南")
    print("=" * 50)
    print()
    
    # 显示各种使用示例
    example_1_single_image()
    example_2_batch_directory()
    example_3_specific_extensions()
    example_4_feature_channels()
    
    # 创建演示脚本
    create_demo_script()
    
    # 显示帮助
    show_help()
    
    print("=" * 50)
    print("快速开始:")
    print("1. 运行测试: python test_adaptive_pool.py")
    print("2. 运行演示: python demo_adaptive_pool.py")
    print("3. 处理您的图像:")
    print("   python adaptive_pool_grayscale.py --input your_dir --output output_dir")

if __name__ == "__main__":
    main()
