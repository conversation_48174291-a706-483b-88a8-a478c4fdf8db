#!/usr/bin/env python3
"""
可视化DE-ViT项目中DINOv2 ViT-Base backbone的特征图
将每个channel保存为单独的灰度图像
"""

import os
import sys
import torch
import torch.nn.functional as F
import numpy as np
import cv2
from PIL import Image
import matplotlib.pyplot as plt
import argparse
from pathlib import Path

# 添加项目路径到sys.path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# 避免循环导入，直接导入需要的模块
try:
    # 方法1：尝试通过detectron2的backbone registry导入
    from detectron2.modeling.backbone import build_backbone
    from detectron2.config import get_cfg
    USE_DETECTRON2_BACKBONE = True
except ImportError:
    USE_DETECTRON2_BACKBONE = False

try:
    # 方法2：直接导入DINOv2模型
    from lib.dinov2.vit import DinoVisionTransformer
    USE_DIRECT_IMPORT = True
except ImportError:
    USE_DIRECT_IMPORT = False


def create_vit_base_model(device='cuda'):
    """
    创建DINOv2 ViT-Base模型，避免循环导入
    """
    print("Creating DINOv2 ViT-Base model...")

    # 手动创建ViT-Base模型参数
    model_config = {
        'patch_size': 14,
        'img_size': 518,
        'in_chans': 3,
        'embed_dim': 768,
        'depth': 12,
        'num_heads': 12,
        'mlp_ratio': 4.0,
        'qkv_bias': True,
        'ffn_bias': True,
        'proj_bias': True,
        'init_values': 1.0,
    }

    # 尝试不同的导入方法
    if USE_DIRECT_IMPORT:
        print("Using direct import method...")
        backbone = DinoVisionTransformer(**model_config)
        # 设置输出层
        backbone._out_indices = [11]  # 最后一层
        backbone._out_features = ['res11']
        backbone._out_feature_channels = {'res11': 768}
        backbone._out_feature_strides = {'res11': 14}

    elif USE_DETECTRON2_BACKBONE:
        print("Using detectron2 backbone registry...")
        cfg = get_cfg()
        cfg.MODEL.BACKBONE.NAME = "build_dino_v2_vit"
        cfg.MODEL.BACKBONE.TYPE = "base"
        cfg.DE.OUT_INDICES = [11]
        backbone = build_backbone(cfg, None)

    else:
        print("Creating model manually...")
        backbone = create_manual_vit_base(**model_config)

    backbone = backbone.to(device)
    backbone.eval()

    print(f"Model loaded successfully on {device}")
    print(f"Model parameters: {sum(p.numel() for p in backbone.parameters()):,}")

    return backbone


def create_manual_vit_base(**config):
    """
    手动创建ViT-Base模型，避免导入问题
    """
    import torch.nn as nn
    from functools import partial

    class PatchEmbed(nn.Module):
        def __init__(self, img_size=224, patch_size=16, in_chans=3, embed_dim=768):
            super().__init__()
            self.img_size = img_size
            self.patch_size = patch_size
            self.patches_resolution = (img_size // patch_size, img_size // patch_size)
            self.num_patches = self.patches_resolution[0] * self.patches_resolution[1]
            self.proj = nn.Conv2d(in_chans, embed_dim, kernel_size=patch_size, stride=patch_size)

        def forward(self, x):
            x = self.proj(x)  # B C H W
            x = x.flatten(2).transpose(1, 2)  # B HW C
            return x

    class Attention(nn.Module):
        def __init__(self, dim, num_heads=8, qkv_bias=False):
            super().__init__()
            self.num_heads = num_heads
            head_dim = dim // num_heads
            self.scale = head_dim ** -0.5
            self.qkv = nn.Linear(dim, dim * 3, bias=qkv_bias)
            self.proj = nn.Linear(dim, dim)

        def forward(self, x):
            B, N, C = x.shape
            qkv = self.qkv(x).reshape(B, N, 3, self.num_heads, C // self.num_heads).permute(2, 0, 3, 1, 4)
            q, k, v = qkv[0], qkv[1], qkv[2]

            attn = (q @ k.transpose(-2, -1)) * self.scale
            attn = attn.softmax(dim=-1)

            x = (attn @ v).transpose(1, 2).reshape(B, N, C)
            x = self.proj(x)
            return x

    class MLP(nn.Module):
        def __init__(self, in_features, hidden_features=None, out_features=None):
            super().__init__()
            out_features = out_features or in_features
            hidden_features = hidden_features or in_features
            self.fc1 = nn.Linear(in_features, hidden_features)
            self.act = nn.GELU()
            self.fc2 = nn.Linear(hidden_features, out_features)

        def forward(self, x):
            x = self.fc1(x)
            x = self.act(x)
            x = self.fc2(x)
            return x

    class Block(nn.Module):
        def __init__(self, dim, num_heads, mlp_ratio=4., qkv_bias=False):
            super().__init__()
            self.norm1 = nn.LayerNorm(dim)
            self.attn = Attention(dim, num_heads=num_heads, qkv_bias=qkv_bias)
            self.norm2 = nn.LayerNorm(dim)
            mlp_hidden_dim = int(dim * mlp_ratio)
            self.mlp = MLP(in_features=dim, hidden_features=mlp_hidden_dim)

        def forward(self, x):
            x = x + self.attn(self.norm1(x))
            x = x + self.mlp(self.norm2(x))
            return x

    class SimpleViT(nn.Module):
        def __init__(self, img_size=518, patch_size=14, in_chans=3, embed_dim=768,
                     depth=12, num_heads=12, mlp_ratio=4., qkv_bias=True, **kwargs):
            super().__init__()
            self.patch_embed = PatchEmbed(img_size, patch_size, in_chans, embed_dim)
            num_patches = self.patch_embed.num_patches

            self.cls_token = nn.Parameter(torch.zeros(1, 1, embed_dim))
            self.pos_embed = nn.Parameter(torch.zeros(1, num_patches + 1, embed_dim))

            self.blocks = nn.ModuleList([
                Block(embed_dim, num_heads, mlp_ratio, qkv_bias)
                for _ in range(depth)
            ])
            self.norm = nn.LayerNorm(embed_dim)

            # 初始化权重
            torch.nn.init.trunc_normal_(self.pos_embed, std=0.02)
            torch.nn.init.trunc_normal_(self.cls_token, std=0.02)

        def forward(self, x):
            B = x.shape[0]
            x = self.patch_embed(x)

            cls_tokens = self.cls_token.expand(B, -1, -1)
            x = torch.cat((cls_tokens, x), dim=1)
            x = x + self.pos_embed

            for blk in self.blocks:
                x = blk(x)

            x = self.norm(x)

            # 返回patch tokens (去除cls token)
            patch_tokens = x[:, 1:]

            # 模拟detectron2的输出格式
            return {'res11': patch_tokens}

    return SimpleViT(**config)


def preprocess_image(image_path, target_size=800, max_size=1333):
    """
    预处理图像，模拟DE-ViT项目的图像预处理流程
    """
    # 读取图像
    if isinstance(image_path, str):
        image = cv2.imread(image_path)
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    else:
        image = image_path
    
    print(f"Original image shape: {image.shape}")
    
    # 应用ResizeShortestEdge变换 (模拟测试时的预处理)
    h, w = image.shape[:2]
    
    # 计算缩放比例
    scale = target_size * 1.0 / min(h, w)
    if h < w:
        newh, neww = target_size, int(scale * w + 0.5)
    else:
        newh, neww = int(scale * h + 0.5), target_size
    
    # 检查长边是否超限
    if max(newh, neww) > max_size:
        scale = max_size * 1.0 / max(newh, neww)
        newh = int(newh * scale + 0.5)
        neww = int(neww * scale + 0.5)
    
    # 调整图像大小
    image_resized = cv2.resize(image, (neww, newh), interpolation=cv2.INTER_LINEAR)
    print(f"Resized image shape: {image_resized.shape}")
    
    # 转换为tensor并归一化 (使用DINOv2的标准化参数)
    image_tensor = torch.from_numpy(image_resized).float().permute(2, 0, 1) / 255.0
    
    # DINOv2标准化参数
    mean = torch.tensor([0.485, 0.456, 0.406]).view(3, 1, 1)
    std = torch.tensor([0.229, 0.224, 0.225]).view(3, 1, 1)
    
    image_tensor = (image_tensor - mean) / std
    
    # 添加batch维度
    image_tensor = image_tensor.unsqueeze(0)
    
    return image_tensor, (newh, neww)


def extract_features(backbone, image_tensor, device='cuda'):
    """
    从backbone提取特征
    """
    image_tensor = image_tensor.to(device)

    print("Extracting features from backbone...")
    with torch.no_grad():
        # 获取特征 (模拟DE-ViT中的特征提取)
        features = backbone(image_tensor)

        # 处理不同的输出格式
        if isinstance(features, dict):
            # detectron2格式输出
            feature_key = list(features.keys())[-1]  # 通常是'res11'
            patch_features = features[feature_key]  # [B, H*W, C]
            print(f"Feature key: {feature_key}")
        else:
            # 直接tensor输出
            patch_features = features
            print("Direct tensor output")

        print(f"Patch features shape: {patch_features.shape}")

    return patch_features


def reshape_features_to_spatial(patch_features, image_size, patch_size=14):
    """
    将patch特征重新整形为空间特征图
    """
    B, N, C = patch_features.shape
    H, W = image_size
    
    # 计算patch网格大小
    patch_h = H // patch_size
    patch_w = W // patch_size
    
    print(f"Patch grid size: {patch_h} x {patch_w}")
    print(f"Expected patches: {patch_h * patch_w}, Actual patches: {N}")
    
    # 重新整形为空间特征图 [B, C, patch_h, patch_w]
    spatial_features = patch_features.view(B, patch_h, patch_w, C).permute(0, 3, 1, 2)
    
    print(f"Spatial features shape: {spatial_features.shape}")
    
    return spatial_features


def visualize_and_save_channels(spatial_features, output_dir, image_name):
    """
    可视化并保存每个channel的特征图
    """
    output_dir = Path(output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 移除batch维度
    features = spatial_features.squeeze(0)  # [C, H, W]
    C, H, W = features.shape
    
    print(f"Visualizing {C} channels...")
    print(f"Feature map size: {H} x {W}")
    
    # 为每个channel创建子目录
    channel_dir = output_dir / f"{image_name}_channels"
    channel_dir.mkdir(exist_ok=True)
    
    # 保存每个channel
    for c in range(C):
        channel_feature = features[c].cpu().numpy()  # [H, W]
        
        # 归一化到0-255
        channel_min = channel_feature.min()
        channel_max = channel_feature.max()
        
        if channel_max > channel_min:
            channel_normalized = (channel_feature - channel_min) / (channel_max - channel_min)
        else:
            channel_normalized = np.zeros_like(channel_feature)
        
        channel_uint8 = (channel_normalized * 255).astype(np.uint8)
        
        # 保存为灰度图
        channel_path = channel_dir / f"channel_{c:03d}.png"
        cv2.imwrite(str(channel_path), channel_uint8)
        
        # 每100个channel打印一次进度
        if (c + 1) % 100 == 0 or c == C - 1:
            print(f"Saved {c + 1}/{C} channels")
    
    print(f"All channels saved to: {channel_dir}")
    
    # 创建一个总览图 (显示前64个channel)
    create_overview_image(features, output_dir, image_name)


def create_overview_image(features, output_dir, image_name, max_channels=64):
    """
    创建特征图总览 (显示前N个channel)
    """
    C, H, W = features.shape
    num_channels = min(C, max_channels)
    
    # 计算网格大小
    grid_size = int(np.ceil(np.sqrt(num_channels)))
    
    fig, axes = plt.subplots(grid_size, grid_size, figsize=(20, 20))
    fig.suptitle(f'Feature Maps Overview - First {num_channels} Channels', fontsize=16)
    
    for i in range(grid_size * grid_size):
        row = i // grid_size
        col = i % grid_size
        
        if i < num_channels:
            channel_feature = features[i].cpu().numpy()
            axes[row, col].imshow(channel_feature, cmap='gray')
            axes[row, col].set_title(f'Channel {i}', fontsize=8)
        else:
            axes[row, col].axis('off')
        
        axes[row, col].set_xticks([])
        axes[row, col].set_yticks([])
    
    plt.tight_layout()
    overview_path = output_dir / f"{image_name}_overview.png"
    plt.savefig(overview_path, dpi=150, bbox_inches='tight')
    plt.close()
    
    print(f"Overview image saved to: {overview_path}")


def main():
    parser = argparse.ArgumentParser(description='Visualize DINOv2 ViT-Base backbone features')
    parser.add_argument('--image', type=str, required=True, help='Path to input image')
    parser.add_argument('--output', type=str, required=True, help='Output directory for feature visualizations')
    parser.add_argument('--device', type=str, default='cuda', choices=['cuda', 'cpu'], help='Device to use')
    parser.add_argument('--target_size', type=int, default=800, help='Target size for shortest edge')
    parser.add_argument('--max_size', type=int, default=1333, help='Maximum size for longest edge')
    
    args = parser.parse_args()
    
    # 检查输入文件
    if not os.path.exists(args.image):
        print(f"Error: Image file {args.image} not found!")
        return
    
    # 检查设备
    if args.device == 'cuda' and not torch.cuda.is_available():
        print("CUDA not available, using CPU instead")
        args.device = 'cpu'
    
    print(f"Using device: {args.device}")
    
    # 获取图像名称
    image_name = Path(args.image).stem
    
    try:
        # 1. 加载backbone
        backbone = create_vit_base_model(args.device)
        
        # 2. 预处理图像
        image_tensor, image_size = preprocess_image(args.image, args.target_size, args.max_size)
        
        # 3. 提取特征
        patch_features = extract_features(backbone, image_tensor, args.device)
        
        # 4. 重新整形为空间特征图
        spatial_features = reshape_features_to_spatial(patch_features, image_size)
        
        # 5. 可视化并保存
        visualize_and_save_channels(spatial_features, args.output, image_name)
        
        print("\n=== Feature Visualization Complete ===")
        print(f"Input image: {args.image}")
        print(f"Output directory: {args.output}")
        print(f"Number of channels: {spatial_features.shape[1]}")
        print(f"Feature map size: {spatial_features.shape[2]} x {spatial_features.shape[3]}")
        
    except Exception as e:
        print(f"Error during processing: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
