@configurable  # Detectron2 的装饰器，表示该类的构造函数参数可以通过 cfg 对象配置
def __init__(self,
             offline_backbone: Backbone,  # 用于生成候选区域的离线骨干网络 (RPN 使用)
             backbone: Backbone,  # 模型主要的特征提取骨干网络 (如 DINOv2 ViT)
             offline_proposal_generator: nn.Module,  # 离线的候选区域生成器 (RPN)

             pixel_mean: Tuple[float],  # 主骨干网络输入图像进行归一化时使用的均值
             pixel_std: Tuple[float],  # 主骨干网络输入图像进行归一化时使用的标准差

             offline_pixel_mean: Tuple[float],  # 离线 RPN 输入图像归一化均值
             offline_pixel_std: Tuple[float],  # 离线 RPN 输入图像归一化标准差
             offline_input_format: Optional[str] = None,  # 离线 RPN 输入图像格式 (如 "RGB", "BGR")

             class_prototypes_file="",  # 类别原型文件的路径，可以是单个或多个（逗号分隔）
             bg_prototypes_file="",  # 背景原型文件的路径
             roialign_size=14,  # ROI Align 操作输出特征图的空间尺寸 (论文中的 K)
             box_noise_scale=1.0,  # 训练时对真实边界框添加噪声的尺度，用于数据增强
             proposal_matcher=None,  # 训练时用于将提议框与真实框匹配的匹配器

             box2box_transform=None,  # 用于边界框坐标变换（如绝对坐标与相对偏移量之间转换）
             smooth_l1_beta=0.0,  # Smooth L1 损失函数的 beta 参数，用于边界框回归
             test_score_thresh=0.001,  # 测试时过滤检测结果的得分阈值
             test_nms_thresh=0.5,  # 测试时进行非极大值抑制 (NMS) 的 IoU 阈值
             test_topk_per_image=100,  # 测试时每张图像保留的最高分检测结果数量
             cls_temp=0.1,  # 分类 logits 的温度系数，调整 softmax 平滑度

             num_sample_class=-1,  # 推理时为 Top-T 个最可能类别创建子空间特征，T 的值
             seen_cids=[],  # 训练时可见的基础类别的ID列表
             all_cids=[],  # 数据集中所有类别（基础类+新类）的ID列表
             T_length=128,  # channel-reorder 中对其他类别相似度进行插值时的目标长度

             input_feat_dim=1024,  # 输入 ViT 特征的维度
             proj_feat_dim=256,  # 原始 ROI 特征经过投影后的目标维度
             pos_emb_size=128,  # 当前类别/背景原型点积结果的嵌入维度
             dist_emb_size=128,  # 其他类别原型点积结果的嵌入维度
             hidden_size=256,  # 区域传播网络中隐藏层的特征维度
             num_layers=4,  # 区域传播网络 (RegionPropagationNetwork) 的层数

             bg_cls_weight=0.2,  # 背景类别的分类损失权重
             batch_size_per_image=128,  # 训练时每张图片采样用于后续处理的区域数量 (RCNN阶段的batch size)
             pos_ratio=0.25,  # 在上述采样中，正样本（前景）所占的比例
             mult_rpn_score=False,  # 是否将 RPN 的 objectness 分数乘到最终分类分数上
             ):
    super().__init__()  # 调用父类 torch.nn.Module 的构造函数

    # 如果 class_prototypes_file 是一个包含多个路径的字符串（用逗号分隔），则将其分割成列表
    if ',' in class_prototypes_file:
        class_prototypes_file = class_prototypes_file.split(',')

    # 将主骨干网络的图像归一化均值和标准差注册为模型的缓冲区 (buffer)
    # 缓冲区是模型状态的一部分，但不是通过反向传播学习的参数
    # .view(-1, 1, 1) 是为了匹配图像张量 (NCHW) 的形状以便广播
    self.register_buffer("pixel_mean", torch.tensor(pixel_mean).view(-1, 1, 1), False)
    self.register_buffer("pixel_std", torch.tensor(pixel_std).view(-1, 1, 1), False)

    self.backbone = backbone  # 存储传入的主特征提取骨干网络 (如 DINOv2 ViT)
    self.bg_cls_weight = bg_cls_weight  # 存储背景分类损失权重
    self.box2box_transform = box2box_transform  # 存储边界框坐标变换器

    # 根据 pixel_mean 的值判断输入图像的像素值是否需要从 [0, 255] 范围转换到 [0, 1] 范围
    if np.sum(pixel_mean) < 3.0:  # 这是一个启发式判断，如果均值很小，则认为像素值在0-1范围，需要先乘以255
        self.div_pixel = True  # 表示输入像素值需要除以 255.0
    else:
        self.div_pixel = False  # 表示输入像素值已经是 0-255 范围或者已经处理过

    # --- RPN 相关设置 (离线 RPN，用于生成候选区域) ---
    self.input_format = "RGB"  # 主骨干网络期望的输入图像格式
    self.offline_backbone = offline_backbone  # 存储离线 RPN 使用的骨干网络
    self.offline_proposal_generator = offline_proposal_generator  # 存储离线 RPN 模型

    # 如果为离线 RPN 提供了输入格式和归一化参数，则进行设置
    if offline_input_format and offline_pixel_mean and offline_pixel_std:
        self.offline_input_format = offline_input_format
        self.register_buffer("offline_pixel_mean", torch.tensor(offline_pixel_mean).view(-1, 1, 1), False)
        self.register_buffer("offline_pixel_std", torch.tensor(offline_pixel_std).view(-1, 1, 1), False)
        # 同样判断离线 RPN 的输入像素值是否需要除以 255.0
        if np.sum(offline_pixel_mean) < 3.0:
            assert offline_input_format == 'RGB'  # 通常0-1范围的像素值对应RGB格式
            self.offline_div_pixel = True
        else:
            self.offline_div_pixel = False

    self.proposal_matcher = proposal_matcher  # 存储提议框匹配器，用于训练时匹配提议和真值

    # --- 加载和处理类别原型 (Class Prototypes) ---
    if isinstance(class_prototypes_file, str):  # 如果只提供了一个原型文件路径
        dct = torch.load(class_prototypes_file)  # 加载原型文件 (通常是 .pkl 或 .pth)
        prototypes = dct['prototypes']  # 提取原型张量
        if 'label_names' not in dct:  # 如果文件中没有类别名称
            warnings.warn("label_names not found in class_prototypes_file, using COCO_SEEN_CLS + COCO_UNSEEN_CLS")
            # 使用预定义的 COCO 基础类和新类名称作为默认
            prototype_label_names = COCO_SEEN_CLS + COCO_UNSEEN_CLS
            assert len(prototype_label_names) == len(prototypes)  # 确保名称数量与原型数量一致
        else:
            prototype_label_names = dct['label_names']  # 使用文件中的类别名称
    elif isinstance(class_prototypes_file, list):  # 如果提供了多个原型文件路径 (例如，基础类和新类的原型分开存储)
        # 加载第一个原型文件 (通常是基础类原型)
        p1 = torch.load(class_prototypes_file[0])
        # 加载第二个原型文件 (通常是新类原型)
        p2 = torch.load(class_prototypes_file[1])
        # 将两组原型在类别维度上拼接起来
        prototypes = torch.cat([p1['prototypes'], p2['prototypes']], dim=0)
        # 将两组类别名称列表拼接起来
        prototype_label_names = p1['label_names'] + p2['label_names']
    else:
        raise NotImplementedError("class_prototypes_file format not supported")

    # 对加载的原型进行 L2 归一化，得到用于计算相似度的类别权重
    if len(prototypes.shape) == 3:  # 如果原型是三维的 (例如，每个类别有多个原型样本)
        class_weights = F.normalize(prototypes.mean(dim=1), dim=-1)  # 先在样本维度上取平均，再归一化
    else:  # 如果原型是二维的 (每个类别一个原型向量)
        class_weights = F.normalize(prototypes, dim=-1)  # 直接归一化

    self.num_train_classes = len(seen_cids)  # 训练时可见的基础类别的数量
    self.num_classes = len(all_cids)  # 数据集中所有类别的总数 (基础类 + 新类)

    # 根据 seen_cids (训练时基础类名) 和 all_cids (测试时所有类名)
    # 获取这些类别在 prototype_label_names (从原型文件加载的名称列表) 中的索引顺序
    # 这是为了确保模型内部使用的类别原型顺序与类别ID对应
    train_class_order = [prototype_label_names.index(c) for c in seen_cids]
    test_class_order = [prototype_label_names.index(c) for c in all_cids]

    self.label_names = prototype_label_names  # 存储所有原型对应的类别名称
    assert -1 not in train_class_order and -1 not in test_class_order  # 确保所有需要的类别都在原型文件中找到了

    # 根据计算得到的顺序，提取并注册训练时和测试时使用的类别原型 (类别权重)
    self.register_buffer("train_class_weight", class_weights[torch.as_tensor(train_class_order)])
    self.register_buffer("test_class_weight", class_weights[torch.as_tensor(test_class_order)])
    self.test_class_order = test_class_order  # 存储测试时的类别顺序 (索引)

    self.all_labels = all_cids  # 存储所有类别的名称列表
    self.seen_labels = seen_cids  # 存储基础类别的名称列表

    # --- 加载背景原型 (Background Prototypes) ---
    bg_protos = torch.load(bg_prototypes_file)  # 加载背景原型文件
    if isinstance(bg_protos, dict):  # 如果是字典格式 (例如 generate_prototypes 的输出)
        bg_protos = bg_protos['prototypes']  # 提取原型张量
    if len(bg_protos.shape) == 3:  # 如果背景原型也是三维的
        bg_protos = bg_protos.flatten(0, 1)  # 将前两个维度展平 (例如，将多个背景样本的原型合并)
    self.register_buffer("bg_tokens", bg_protos)  # 注册背景原型
    self.num_bg_tokens = len(self.bg_tokens)  # 存储背景原型的数量

    # --- ROI Align 层和其他参数 ---
    self.roialign_size = roialign_size  # ROI Align 输出特征图的空间尺寸 (K)
    # 初始化一个 7x7 输出的 ROIAlign 层，可能用于快速初始分类或提议筛选
    # 1 / backbone.patch_size 是空间尺度因子，将在提议框坐标从图像空间映射到特征图空间时使用
    # sampling_ratio=-1 通常表示使用自适应（或精确）采样
    self.roi_align_77 = ROIAlign(7, 1 / backbone.patch_size, sampling_ratio=-1)
    # 初始化一个 KxK 输出的 ROIAlign 层，用于提取送入区域传播网络的主要 ROI 特征
    self.roi_align = ROIAlign(roialign_size, 1 / backbone.patch_size, sampling_ratio=-1)

    # 存储其他从配置中传入的参数
    self.box_noise_scale = box_noise_scale  # 训练时边界框噪声的尺度
    self.smooth_l1_beta = smooth_l1_beta  # Smooth L1 回归损失的 beta 参数
    self.test_score_thresh = test_score_thresh  # 测试时检测框的得分阈值
    self.test_nms_thresh = test_nms_thresh  # 测试时 NMS 的 IoU 阈值
    self.test_topk_per_image = test_topk_per_image  # 测试时每张图片保留的检测框数量上限

    # --- 特征子空间投影和区域传播网络相关的参数和层 ---
    self.T = T_length  # channel-reorder 中其他类别特征插值后的目标通道长度 T
    self.input_feat_dim = input_feat_dim  # 输入 ViT ROI 特征的维度
    self.proj_feat_dim = proj_feat_dim  # 原始 ROI 特征经过一个线性层投影后的目标维度
    self.pos_emb_size = pos_emb_size  # 与当前类别原型点积、或与背景原型点积后的特征，再经过线性层变换后的嵌入维度
    self.dist_emb_size = dist_emb_size  # 与其他类别原型点积（排序插值后）、或所有类别原型点积（排序插值后）的特征，再经过线性层变换后的嵌入维度
    self.hidden_size = hidden_size  # 区域传播网络 (RPN) 内部隐藏层的特征维度

    # 定义用于特征子空间投影的线性层，使用 nn.ModuleDict 方便管理
    self.foreground_linears = nn.ModuleDict({
        # 处理 pc·h_vit (当前类别原型点积) 或其嵌入的线性层
        'current_class': nn.Linear(pos_emb_size, dist_emb_size),
        # 处理 channel-reorder(p_{C\c}·h_vit) (其他类别原型点积，排序插值到 T_length) 的线性层
        'other_classes': nn.Linear(T_length, dist_emb_size),
        # 处理 p_B·h_vit (背景原型点积) 的线性层
        'background': nn.Linear(self.num_bg_tokens, pos_emb_size),
        # 处理原始 ROI 特征 h_vit 的线性投影层
        'feat': nn.Linear(input_feat_dim, proj_feat_dim)
    })
    # 为背景处理路径定义类似的线性层集合
    self.background_linears = nn.ModuleDict({
        'classes': nn.Linear(T_length, dist_emb_size),  # 处理所有类别原型点积 (排序插值后)
        'background': nn.Linear(self.num_bg_tokens, pos_emb_size),  # 处理背景原型点积
        'feat': nn.Linear(input_feat_dim, proj_feat_dim)  # 处理原始 ROI 特征
    })

    # 初始化前景路径的区域传播网络 (RegionPropagationNetwork)
    # 输入维度是拼接了上述 foreground_linears 各部分输出后的总维度
    self.rpropnet = RegionPropagationNetwork(num_layers, proj_feat_dim + 2 * dist_emb_size + pos_emb_size,
                                             hidden_size, roialign_size)
    # 初始化背景路径的区域传播网络，可能只用于分类
    self.rpropnet_bg = RegionPropagationNetwork(num_layers, proj_feat_dim + dist_emb_size + pos_emb_size,
                                                hidden_size, roialign_size, classification_only=True)

    # --- 其他参数存储 ---
    self.cls_temp = cls_temp  # 分类 logits 的温度系数
    self.num_sample_class = num_sample_class  # 推理时选择 Top-T 个类别的 T 值
    self.batch_size_per_image = batch_size_per_image  # 训练时每张图片送入 RCNN 头的区域数量
    self.pos_ratio = pos_ratio  # 在上述采样中，正样本（前景）所占的比例
    self.mult_rpn_score = mult_rpn_score  # 是否将 RPN 的 objectness 分数乘到最终分类分数上