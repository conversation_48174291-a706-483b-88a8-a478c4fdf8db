#!/usr/bin/env python3
"""
简单的Hook测试 - 直接测试PropagateNet类
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import torch
import torch.nn as nn
import numpy as np

# 正确导入PropagateNet类
from detectron2.modeling.meta_arch.devit import PropagateNet

def test_propagate_net():
    """直接测试PropagateNet类"""
    print("Testing PropagateNet class directly...")
    
    # 创建PropagateNet实例
    input_dim = 256
    hidden_dim = 256
    num_layers = 3
    
    prop_net = PropagateNet(
        input_dim=input_dim,
        hidden_dim=hidden_dim,
        has_input_mask=False,
        num_layers=num_layers,
        dropout=0.5,
        mask_temperature=0.1
    )
    
    print(f"✓ Created PropagateNet:")
    print(f"  Input dim: {input_dim}")
    print(f"  Hidden dim: {hidden_dim}")
    print(f"  Num layers: {num_layers}")
    print(f"  Main layers: {len(prop_net.main_layers)}")
    
    # 查看第一层结构
    first_layer = prop_net.main_layers[0]
    print(f"\nFirst layer structure:")
    for i, (name, module) in enumerate(first_layer.named_children()):
        print(f"  {i}: {name} -> {module}")
    
    # 注册Hook到第一层的Conv2d
    captured_features = {}
    
    def hook_fn(module, input, output):
        captured_features['conv_output'] = output.detach()
        print(f"🎯 Hook captured: {output.shape}")
    
    # 找到第一个Conv2d层并注册hook
    for name, module in first_layer.named_children():
        if isinstance(module, nn.Conv2d):
            hook = module.register_forward_hook(hook_fn)
            print(f"✓ Hook registered to: {name} (Conv2d)")
            break
    
    # 创建测试输入
    batch_size = 2
    height, width = 14, 14
    test_input = torch.randn(batch_size, input_dim, height, width)
    
    print(f"\nTesting with input shape: {test_input.shape}")
    
    # 前向传播
    prop_net.eval()
    with torch.no_grad():
        output = prop_net(test_input)
    
    print(f"✓ Forward pass successful!")
    print(f"Output type: {type(output)}")
    if isinstance(output, torch.Tensor):
        print(f"Output shape: {output.shape}")
    elif isinstance(output, list):
        print(f"Output list length: {len(output)}")
        for i, item in enumerate(output):
            print(f"  Item {i}: {item.shape if hasattr(item, 'shape') else type(item)}")
    
    # 检查捕获的特征
    if 'conv_output' in captured_features:
        features = captured_features['conv_output']
        print(f"\n🎉 Successfully captured first layer Conv2d output!")
        print(f"Feature shape: {features.shape}")
        print(f"Feature stats:")
        print(f"  Min: {features.min().item():.4f}")
        print(f"  Max: {features.max().item():.4f}")
        print(f"  Mean: {features.mean().item():.4f}")
        print(f"  Std: {features.std().item():.4f}")
        
        # 简单可视化测试
        print(f"\nTesting feature visualization...")
        try:
            import matplotlib.pyplot as plt
            
            # 取第一个batch的前4个通道
            sample_features = features[0, :4]  # [4, H, W]
            
            fig, axes = plt.subplots(2, 2, figsize=(8, 8))
            axes = axes.flatten()
            
            for i in range(4):
                feature_map = sample_features[i].numpy()
                
                # 归一化
                if feature_map.max() > feature_map.min():
                    feature_map = (feature_map - feature_map.min()) / (feature_map.max() - feature_map.min())
                
                axes[i].imshow(feature_map, cmap='viridis')
                axes[i].set_title(f'Channel {i}')
                axes[i].axis('off')
            
            plt.tight_layout()
            plt.savefig('simple_hook_test_features.png', dpi=150, bbox_inches='tight')
            plt.close()
            
            print(f"✓ Saved test visualization: simple_hook_test_features.png")
            
        except ImportError:
            print("⚠️  Matplotlib not available, skipping visualization")
        except Exception as e:
            print(f"⚠️  Visualization failed: {e}")
        
        return True
    else:
        print(f"❌ No features captured by hook!")
        return False

def test_hook_mechanism():
    """测试基本的Hook机制"""
    print("Testing basic hook mechanism...")
    
    # 创建简单的测试网络
    class SimpleNet(nn.Module):
        def __init__(self):
            super().__init__()
            self.conv = nn.Conv2d(3, 16, 3, padding=1)
            self.relu = nn.ReLU()
        
        def forward(self, x):
            x = self.conv(x)
            x = self.relu(x)
            return x
    
    net = SimpleNet()
    
    # 注册hook
    captured = {}
    def hook_fn(module, input, output):
        captured['conv'] = output.detach()
        print(f"Basic hook captured: {output.shape}")
    
    hook = net.conv.register_forward_hook(hook_fn)
    
    # 测试
    test_input = torch.randn(1, 3, 32, 32)
    with torch.no_grad():
        output = net(test_input)
    
    hook.remove()
    
    if 'conv' in captured:
        print(f"✓ Basic hook mechanism works!")
        print(f"Captured shape: {captured['conv'].shape}")
        return True
    else:
        print(f"❌ Basic hook mechanism failed!")
        return False

def main():
    print("Simple Hook Test for DE-ViT Propagation Layer")
    print("=" * 50)
    
    try:
        # 1. 测试基本Hook机制
        print("Step 1: Testing basic hook mechanism...")
        if not test_hook_mechanism():
            print("❌ Basic hook test failed!")
            return
        
        print("\n" + "-" * 30 + "\n")
        
        # 2. 测试PropagateNet
        print("Step 2: Testing PropagateNet with hooks...")
        if test_propagate_net():
            print("\n🎉 All tests passed!")
            print("\nThis means the hook mechanism should work for:")
            print("1. Capturing first layer Conv2d output from PropagateNet")
            print("2. Visualizing the captured features")
            print("\nYou can now use the main visualization script:")
            print("python visualize_propagation_hook.py --image your_image.jpg --model your_model.pth")
        else:
            print("\n❌ PropagateNet test failed!")
            print("The hook mechanism may need adjustment for the full model.")
    
    except Exception as e:
        print(f"\n💥 Test crashed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
