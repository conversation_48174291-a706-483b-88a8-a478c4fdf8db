#!/usr/bin/env python3
"""
基于DINO预训练特征的经典CAM分类网络
结合DE-ViT项目中的DINO backbone和经典CAM架构
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import cv2
import matplotlib.pyplot as plt
from pathlib import Path

class DINO_CAM_Classifier(nn.Module):
    """
    基于DINO的CAM分类网络
    
    架构:
    Input -> DINO(frozen) -> Conv3x3 -> GAP -> Linear -> Output
                                ↑
                            CAM特征层
    """
    
    def __init__(self, dino_model, num_classes, dino_output_dim=768, cam_feature_dim=512):
        """
        Args:
            dino_model: 预训练的DINO模型 (来自DE-ViT项目)
            num_classes: 分类类别数
            dino_output_dim: DINO输出特征维度 (ViT-S: 384, ViT-B: 768)
            cam_feature_dim: CAM特征层维度
        """
        super(DINO_CAM_Classifier, self).__init__()
        
        # ========== 特征提取部分 (冻结) ==========
        self.dino_backbone = dino_model
        self.dino_output_dim = dino_output_dim
        
        # 冻结DINO参数
        for param in self.dino_backbone.parameters():
            param.requires_grad = False
        
        print(f"✓ DINO backbone frozen with {dino_output_dim}D features")
        
        # ========== CAM分类部分 ==========
        # 1. 卷积层 - 将DINO特征转换为CAM特征
        self.cam_conv = nn.Conv2d(
            in_channels=dino_output_dim,
            out_channels=cam_feature_dim,
            kernel_size=3,
            stride=1,
            padding=1,
            bias=False
        )
        self.cam_bn = nn.BatchNorm2d(cam_feature_dim)
        self.cam_relu = nn.ReLU(inplace=True)
        
        # 2. 全局平均池化 - CAM的关键组件
        self.global_avg_pool = nn.AdaptiveAvgPool2d(1)
        
        # 3. 分类头 - 权重用于CAM计算
        self.classifier = nn.Linear(cam_feature_dim, num_classes)
        
        # 初始化新增的层
        self._initialize_weights()
        
        print(f"✓ CAM classifier initialized: {cam_feature_dim}D -> {num_classes} classes")
    
    def _initialize_weights(self):
        """初始化新增层的权重"""
        # 卷积层初始化
        nn.init.kaiming_normal_(self.cam_conv.weight, mode='fan_out', nonlinearity='relu')
        
        # 分类器初始化
        nn.init.normal_(self.classifier.weight, 0, 0.01)
        nn.init.constant_(self.classifier.bias, 0)
    
    def forward(self, x, return_features=False):
        """
        前向传播
        
        Args:
            x: 输入图像 [B, 3, H, W]
            return_features: 是否返回CAM特征图
            
        Returns:
            logits: 分类logits [B, num_classes]
            cam_features: CAM特征图 [B, cam_feature_dim, H', W'] (可选)
        """
        batch_size = x.size(0)
        
        # ========== DINO特征提取 (冻结) ==========
        with torch.no_grad():
            dino_features = self.dino_backbone(x)  # [B, dino_dim, H', W']
        
        # ========== CAM分类部分 ==========
        # 1. 卷积层处理DINO特征
        cam_features = self.cam_conv(dino_features)  # [B, cam_dim, H', W']
        cam_features = self.cam_bn(cam_features)
        cam_features = self.cam_relu(cam_features)
        
        # 2. 全局平均池化
        pooled_features = self.global_avg_pool(cam_features)  # [B, cam_dim, 1, 1]
        pooled_features = pooled_features.view(batch_size, -1)  # [B, cam_dim]
        
        # 3. 分类
        logits = self.classifier(pooled_features)  # [B, num_classes]
        
        if return_features:
            return logits, cam_features
        else:
            return logits
    
    def generate_cam(self, x, target_class=None):
        """
        生成CAM热力图
        
        Args:
            x: 输入图像 [B, 3, H, W]
            target_class: 目标类别索引，None表示使用预测类别
            
        Returns:
            cam: CAM热力图 [H', W']
            predicted_class: 预测类别
            confidence: 预测置信度
        """
        # 前向传播获取特征和预测
        logits, cam_features = self.forward(x, return_features=True)
        
        # 获取预测结果
        probs = F.softmax(logits, dim=1)
        predicted_class = torch.argmax(logits, dim=1).item()
        confidence = probs[0, predicted_class].item()
        
        # 确定目标类别
        if target_class is None:
            target_class = predicted_class
        
        # 获取分类器权重
        classifier_weights = self.classifier.weight  # [num_classes, cam_dim]
        target_weights = classifier_weights[target_class]  # [cam_dim]
        
        # 计算CAM
        cam_features_2d = cam_features[0]  # [cam_dim, H', W']
        cam = torch.zeros(cam_features_2d.size(1), cam_features_2d.size(2))
        
        for i in range(cam_features_2d.size(0)):
            cam += target_weights[i] * cam_features_2d[i, :, :]
        
        # ReLU激活
        cam = F.relu(cam)
        
        # 归一化到0-1
        if cam.max() > cam.min():
            cam = (cam - cam.min()) / (cam.max() - cam.min())
        
        return cam.detach().cpu().numpy(), predicted_class, confidence
    
    def get_trainable_parameters(self):
        """获取可训练参数（排除冻结的DINO）"""
        trainable_params = []
        for name, param in self.named_parameters():
            if param.requires_grad:
                trainable_params.append(param)
        return trainable_params
    
    def print_model_info(self):
        """打印模型信息"""
        total_params = sum(p.numel() for p in self.parameters())
        trainable_params = sum(p.numel() for p in self.parameters() if p.requires_grad)
        frozen_params = total_params - trainable_params
        
        print(f"\n{'='*50}")
        print(f"DINO-CAM Classifier Model Info")
        print(f"{'='*50}")
        print(f"Total parameters: {total_params:,}")
        print(f"Trainable parameters: {trainable_params:,}")
        print(f"Frozen parameters (DINO): {frozen_params:,}")
        print(f"Trainable ratio: {trainable_params/total_params*100:.2f}%")
        print(f"{'='*50}\n")

def load_dino_from_devit(dino_checkpoint_path, arch='vit_base'):
    """
    从DE-ViT项目加载预训练的DINO模型
    
    Args:
        dino_checkpoint_path: DINO检查点路径
        arch: 架构类型 ('vit_small', 'vit_base')
        
    Returns:
        dino_model: 加载的DINO模型
        output_dim: 输出特征维度
    """
    import sys
    import os
    
    # 添加DE-ViT项目路径
    devit_path = os.path.dirname(os.path.abspath(__file__))
    sys.path.insert(0, devit_path)
    
    try:
        # 导入DINO相关模块 (根据DE-ViT项目结构调整)
        from detectron2.modeling.backbone import build_backbone
        from detectron2.config import get_cfg
        
        # 设置配置
        cfg = get_cfg()
        cfg.MODEL.BACKBONE.NAME = "build_dino_backbone"
        cfg.MODEL.BACKBONE.DINO_ARCH = arch
        
        # 构建backbone
        dino_model = build_backbone(cfg)
        
        # 加载预训练权重
        if os.path.exists(dino_checkpoint_path):
            checkpoint = torch.load(dino_checkpoint_path, map_location='cpu')
            dino_model.load_state_dict(checkpoint, strict=False)
            print(f"✓ Loaded DINO weights from {dino_checkpoint_path}")
        else:
            print(f"⚠️  DINO checkpoint not found: {dino_checkpoint_path}")
            print("Using randomly initialized DINO model")
        
        # 确定输出维度
        output_dim = 384 if 'small' in arch else 768
        
        return dino_model, output_dim
        
    except ImportError as e:
        print(f"❌ Failed to import DINO from DE-ViT: {e}")
        print("Creating dummy DINO model for testing...")
        return create_dummy_dino(arch), 384 if 'small' in arch else 768

def create_dummy_dino(arch='vit_base'):
    """创建用于测试的虚拟DINO模型"""
    output_dim = 384 if 'small' in arch else 768
    
    class DummyDINO(nn.Module):
        def __init__(self, output_dim):
            super().__init__()
            self.conv = nn.Conv2d(3, output_dim, 16, 16)  # 模拟patch embedding
            
        def forward(self, x):
            # 简单的卷积模拟ViT输出
            features = self.conv(x)  # [B, output_dim, H/16, W/16]
            return features
    
    return DummyDINO(output_dim)

def visualize_cam_result(image, cam, predicted_class, confidence, class_names=None, save_path=None):
    """可视化CAM结果"""
    
    # 准备图像
    if isinstance(image, torch.Tensor):
        if image.dim() == 4:
            image = image[0]  # 移除batch维度
        image = image.permute(1, 2, 0).cpu().numpy()
        image = (image * 255).astype(np.uint8)
    
    # 调整CAM尺寸到图像大小
    cam_resized = cv2.resize(cam, (image.shape[1], image.shape[0]))
    
    # 创建热力图
    heatmap = cv2.applyColorMap(np.uint8(255 * cam_resized), cv2.COLORMAP_JET)
    heatmap = cv2.cvtColor(heatmap, cv2.COLOR_BGR2RGB)
    
    # 叠加图像
    overlay = 0.6 * image + 0.4 * heatmap
    overlay = np.clip(overlay, 0, 255).astype(np.uint8)
    
    # 创建可视化
    fig, axes = plt.subplots(1, 4, figsize=(16, 4))
    
    # 原图
    axes[0].imshow(image)
    axes[0].set_title('Original Image')
    axes[0].axis('off')
    
    # CAM
    axes[1].imshow(cam, cmap='jet')
    axes[1].set_title('Class Activation Map')
    axes[1].axis('off')
    
    # 热力图
    axes[2].imshow(heatmap)
    axes[2].set_title('Heatmap')
    axes[2].axis('off')
    
    # 叠加图
    axes[3].imshow(overlay)
    axes[3].set_title('Overlay')
    axes[3].axis('off')
    
    # 添加预测信息
    class_name = class_names[predicted_class] if class_names else f"Class {predicted_class}"
    fig.suptitle(f'DINO-CAM Result: {class_name} (Confidence: {confidence:.3f})', fontsize=14)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        print(f"✓ Saved CAM visualization: {save_path}")
    
    plt.show()

# 使用示例
def example_usage():
    """使用示例"""
    
    # 1. 加载DINO模型
    dino_model, dino_dim = load_dino_from_devit(
        dino_checkpoint_path="path/to/dino_weights.pth",
        arch='vit_base'
    )
    
    # 2. 创建DINO-CAM分类器
    num_classes = 1000  # ImageNet类别数
    model = DINO_CAM_Classifier(
        dino_model=dino_model,
        num_classes=num_classes,
        dino_output_dim=dino_dim,
        cam_feature_dim=512
    )
    
    # 3. 打印模型信息
    model.print_model_info()
    
    # 4. 测试前向传播
    test_input = torch.randn(1, 3, 224, 224)
    
    # 分类预测
    logits = model(test_input)
    print(f"Output logits shape: {logits.shape}")
    
    # 生成CAM
    cam, pred_class, confidence = model.generate_cam(test_input)
    print(f"CAM shape: {cam.shape}")
    print(f"Predicted class: {pred_class}, Confidence: {confidence:.3f}")
    
    # 5. 可视化结果
    visualize_cam_result(
        image=test_input,
        cam=cam,
        predicted_class=pred_class,
        confidence=confidence,
        save_path="dino_cam_result.png"
    )

if __name__ == "__main__":
    example_usage()
