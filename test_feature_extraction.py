#!/usr/bin/env python3
"""
测试特征提取的简化脚本
"""

import sys
import os
import argparse

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试必要的导入"""
    print("Testing imports...")
    
    try:
        import torch
        print(f"✓ PyTorch: {torch.__version__}")
    except ImportError as e:
        print(f"❌ PyTorch import failed: {e}")
        return False
    
    try:
        import detectron2
        print(f"✓ Detectron2 available")
    except ImportError as e:
        print(f"❌ Detectron2 import failed: {e}")
        return False
    
    try:
        from detectron2.config import get_cfg
        print(f"✓ Detectron2 config available")
    except ImportError as e:
        print(f"❌ Detectron2 config import failed: {e}")
        return False
    
    try:
        from tools.train_net import Trainer
        print(f"✓ Trainer available")
    except ImportError as e:
        print(f"❌ Trainer import failed: {e}")
        return False
    
    return True

def test_config():
    """测试配置加载"""
    print("\nTesting configuration...")
    
    try:
        from detectron2.config import get_cfg
        
        config = get_cfg()
        config_file = 'configs/few-shot/vitb_shot10.yaml'
        
        if not os.path.exists(config_file):
            print(f"❌ Config file not found: {config_file}")
            return False
        
        config.merge_from_file(config_file)
        print(f"✓ Config loaded from: {config_file}")
        
        # 检查关键配置
        print(f"  Model type: {config.MODEL.META_ARCHITECTURE}")
        print(f"  Backbone: {config.MODEL.BACKBONE.NAME}")
        
        # 修复RPN配置问题
        if not hasattr(config.DE, 'OFFLINE_RPN_CONFIG') or not config.DE.OFFLINE_RPN_CONFIG:
            config.DE.OFFLINE_RPN_CONFIG = "configs/RPN/mask_rcnn_R_50_FPN_1x.yaml"
            print(f"  Fixed RPN config: {config.DE.OFFLINE_RPN_CONFIG}")
        
        config.MODEL.DEVICE = 'cpu'
        config.MODEL.WEIGHTS = ''
        config.freeze()
        
        print("✓ Configuration setup successful")
        return True
        
    except Exception as e:
        print(f"❌ Configuration failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_model_creation():
    """测试模型创建"""
    print("\nTesting model creation...")
    
    try:
        from detectron2.config import get_cfg
        from tools.train_net import Trainer
        
        # 设置配置
        config = get_cfg()
        config.merge_from_file('configs/few-shot/vitb_shot10.yaml')
        config.MODEL.DEVICE = 'cpu'
        config.MODEL.WEIGHTS = ''
        
        # 修复RPN配置
        if not hasattr(config.DE, 'OFFLINE_RPN_CONFIG') or not config.DE.OFFLINE_RPN_CONFIG:
            config.DE.OFFLINE_RPN_CONFIG = "configs/RPN/mask_rcnn_R_50_FPN_1x.yaml"
        
        config.freeze()
        
        # 创建模型
        print("Creating model...")
        model = Trainer.build_model(config)
        model.eval()
        
        print("✓ Model created successfully")
        print(f"  Model type: {type(model)}")
        
        # 查找传播层
        print("\nSearching for propagation layers...")
        found_layers = []
        
        for name, module in model.named_modules():
            if 'per_cls_cnn' in name:
                print(f"  Found: {name} -> {type(module)}")
                if hasattr(module, 'main_layers'):
                    print(f"    Has main_layers: {len(module.main_layers)} layers")
                    found_layers.append(name)
        
        if found_layers:
            print(f"✓ Found {len(found_layers)} propagation network(s)")
            return True
        else:
            print("❌ No propagation layers found")
            return False
            
    except Exception as e:
        print(f"❌ Model creation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    parser = argparse.ArgumentParser(description='Test feature extraction setup')
    parser.add_argument('--full-test', action='store_true', 
                       help='Run full test including model creation')
    
    args = parser.parse_args()
    
    print("DE-ViT Feature Extraction Test")
    print("=" * 40)
    
    # 测试导入
    if not test_imports():
        print("\n❌ Import test failed!")
        return 1
    
    # 测试配置
    if not test_config():
        print("\n❌ Configuration test failed!")
        return 1
    
    # 可选的完整测试
    if args.full_test:
        if not test_model_creation():
            print("\n❌ Model creation test failed!")
            return 1
        
        print("\n🎉 All tests passed!")
        print("\nYou can now run the feature extraction script:")
        print("python extract_propagation_features.py \\")
        print("    --image path/to/your/image.jpg \\")
        print("    --model path/to/your/model.pth")
    else:
        print("\n✓ Basic tests passed!")
        print("\nRun with --full-test to test model creation:")
        print("python test_feature_extraction.py --full-test")
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
