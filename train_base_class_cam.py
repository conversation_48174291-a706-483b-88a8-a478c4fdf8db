#!/usr/bin/env python3
"""
使用vitb_shot10.yaml基类训练DINO-CAM分类器
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import yaml
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import torchvision.transforms as transforms
import torchvision.datasets as datasets
from pathlib import Path
import argparse
import time
from dino_cam_classifier import DINO_CAM_Classifier, load_dino_from_devit

def load_config(config_path):
    """加载配置文件"""
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    return config

def create_transforms(config):
    """根据配置创建数据变换"""
    
    # 训练变换
    train_transforms = []
    for transform_config in config['DATA']['TRAIN_TRANSFORMS']:
        for transform_name, params in transform_config.items():
            if transform_name == 'RESIZE':
                train_transforms.append(transforms.Resize(params))
            elif transform_name == 'RANDOM_CROP':
                train_transforms.append(transforms.RandomCrop(params))
            elif transform_name == 'RANDOM_HORIZONTAL_FLIP':
                train_transforms.append(transforms.RandomHorizontalFlip(params))
            elif transform_name == 'COLOR_JITTER':
                train_transforms.append(transforms.ColorJitter(**params))
            elif transform_name == 'NORMALIZE':
                train_transforms.append(transforms.Normalize(**params))
    
    # 验证变换
    val_transforms = []
    for transform_config in config['DATA']['VAL_TRANSFORMS']:
        for transform_name, params in transform_config.items():
            if transform_name == 'RESIZE':
                val_transforms.append(transforms.Resize(params))
            elif transform_name == 'CENTER_CROP':
                val_transforms.append(transforms.CenterCrop(params))
            elif transform_name == 'NORMALIZE':
                val_transforms.append(transforms.Normalize(**params))
    
    # 添加ToTensor
    train_transforms.insert(-1, transforms.ToTensor())
    val_transforms.insert(-1, transforms.ToTensor())
    
    train_transform = transforms.Compose(train_transforms)
    val_transform = transforms.Compose(val_transforms)
    
    return train_transform, val_transform

def create_data_loaders(config):
    """创建数据加载器"""
    
    dataset_dir = config['DATA']['DATASET_DIR']
    batch_size = config['DATA']['BATCH_SIZE']
    num_workers = config['DATA']['NUM_WORKERS']
    
    # 创建变换
    train_transform, val_transform = create_transforms(config)
    
    # 创建数据集
    train_dataset = datasets.ImageFolder(
        root=f"{dataset_dir}/train",
        transform=train_transform
    )
    
    val_dataset = datasets.ImageFolder(
        root=f"{dataset_dir}/val",
        transform=val_transform
    )
    
    # 验证类别数量
    num_classes = len(train_dataset.classes)
    expected_classes = config['BASE_CLASSES']['NUM_CLASSES']
    
    if num_classes != expected_classes:
        print(f"⚠️  Warning: Found {num_classes} classes, expected {expected_classes}")
    
    print(f"✓ Dataset loaded:")
    print(f"  Classes: {num_classes}")
    print(f"  Train samples: {len(train_dataset)}")
    print(f"  Val samples: {len(val_dataset)}")
    print(f"  Class names: {train_dataset.classes}")
    
    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=num_workers,
        pin_memory=True
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=True
    )
    
    return train_loader, val_loader, train_dataset.classes

def create_model(config, num_classes, device):
    """创建DINO-CAM模型"""
    
    # 加载DINO
    dino_weights = config['DINO']['PRETRAINED_WEIGHTS']
    dino_arch = config['DINO']['ARCHITECTURE']
    
    # 转换架构名称
    if dino_arch == 'vit_base':
        arch_name = 'vit_base'
    elif dino_arch == 'vit_small':
        arch_name = 'vit_small'
    elif dino_arch == 'vit_large':
        arch_name = 'vit_large'
    else:
        arch_name = 'vit_base'
    
    print(f"Loading DINO model: {arch_name}")
    dino_model, dino_dim = load_dino_from_devit(dino_weights, arch_name)
    
    # 验证输出维度
    expected_dim = config['DINO']['OUTPUT_DIM']
    if dino_dim != expected_dim:
        print(f"⚠️  Warning: DINO output dim {dino_dim}, expected {expected_dim}")
    
    # 创建DINO-CAM分类器
    model = DINO_CAM_Classifier(
        dino_model=dino_model,
        num_classes=num_classes,
        dino_output_dim=dino_dim,
        cam_feature_dim=config['CAM_CLASSIFIER']['FEATURE_DIM']
    ).to(device)
    
    model.print_model_info()
    
    return model

def create_optimizer_and_scheduler(model, config):
    """创建优化器和学习率调度器"""
    
    # 只优化可训练参数
    trainable_params = model.get_trainable_parameters()
    
    # 创建优化器
    optimizer_name = config['TRAINING']['OPTIMIZER']
    lr = config['TRAINING']['LEARNING_RATE']
    weight_decay = config['TRAINING']['WEIGHT_DECAY']
    
    if optimizer_name == 'Adam':
        optimizer = optim.Adam(trainable_params, lr=lr, weight_decay=weight_decay)
    elif optimizer_name == 'SGD':
        optimizer = optim.SGD(trainable_params, lr=lr, weight_decay=weight_decay, momentum=0.9)
    else:
        raise ValueError(f"Unsupported optimizer: {optimizer_name}")
    
    # 创建学习率调度器
    scheduler_config = config['TRAINING']['LR_SCHEDULER']
    scheduler_type = scheduler_config['TYPE']
    
    if scheduler_type == 'StepLR':
        scheduler = optim.lr_scheduler.StepLR(
            optimizer,
            step_size=scheduler_config['STEP_SIZE'],
            gamma=scheduler_config['GAMMA']
        )
    elif scheduler_type == 'CosineAnnealingLR':
        scheduler = optim.lr_scheduler.CosineAnnealingLR(
            optimizer,
            T_max=config['TRAINING']['EPOCHS']
        )
    else:
        scheduler = None
    
    print(f"✓ Optimizer: {optimizer_name}, LR: {lr}")
    print(f"✓ Scheduler: {scheduler_type}")
    
    return optimizer, scheduler

def train_epoch(model, train_loader, criterion, optimizer, device, epoch, config):
    """训练一个epoch"""
    model.train()
    
    running_loss = 0.0
    correct = 0
    total = 0
    log_period = config['OUTPUT']['LOG_PERIOD']
    
    for batch_idx, (inputs, targets) in enumerate(train_loader):
        inputs, targets = inputs.to(device), targets.to(device)
        
        # 前向传播
        optimizer.zero_grad()
        outputs = model(inputs)
        loss = criterion(outputs, targets)
        
        # 反向传播
        loss.backward()
        optimizer.step()
        
        # 统计
        running_loss += loss.item()
        _, predicted = outputs.max(1)
        total += targets.size(0)
        correct += predicted.eq(targets).sum().item()
        
        # 打印进度
        if batch_idx % log_period == 0:
            print(f'Epoch: {epoch}, Batch: {batch_idx}/{len(train_loader)}, '
                  f'Loss: {loss.item():.4f}, '
                  f'Acc: {100.*correct/total:.2f}%')
    
    epoch_loss = running_loss / len(train_loader)
    epoch_acc = 100. * correct / total
    
    return epoch_loss, epoch_acc

def validate(model, val_loader, criterion, device):
    """验证模型"""
    model.eval()
    
    running_loss = 0.0
    correct = 0
    top5_correct = 0
    total = 0
    
    with torch.no_grad():
        for inputs, targets in val_loader:
            inputs, targets = inputs.to(device), targets.to(device)
            
            outputs = model(inputs)
            loss = criterion(outputs, targets)
            
            running_loss += loss.item()
            
            # Top-1 accuracy
            _, predicted = outputs.max(1)
            total += targets.size(0)
            correct += predicted.eq(targets).sum().item()
            
            # Top-5 accuracy
            _, top5_pred = outputs.topk(5, 1, True, True)
            top5_correct += top5_pred.eq(targets.view(-1, 1).expand_as(top5_pred)).sum().item()
    
    val_loss = running_loss / len(val_loader)
    val_acc = 100. * correct / total
    val_top5_acc = 100. * top5_correct / total
    
    return val_loss, val_acc, val_top5_acc

def save_checkpoint(model, optimizer, epoch, val_acc, config, save_path):
    """保存检查点"""
    checkpoint = {
        'epoch': epoch,
        'model_state_dict': model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'val_acc': val_acc,
        'config': config,
        'base_classes': config['BASE_CLASSES']['NAMES']
    }
    torch.save(checkpoint, save_path)
    print(f"✓ Checkpoint saved: {save_path}")

def main():
    parser = argparse.ArgumentParser(description='Train DINO-CAM with base classes')
    parser.add_argument('--config', type=str, 
                       default='configs/classification/dino_cam_base_classes.yaml',
                       help='Path to config file')
    parser.add_argument('--device', type=str, default='cuda',
                       help='Device to use')
    
    args = parser.parse_args()
    
    # 加载配置
    config = load_config(args.config)
    print(f"✓ Config loaded from: {args.config}")
    
    # 设置设备
    device = torch.device(args.device if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # 创建输出目录
    save_dir = Path(config['OUTPUT']['SAVE_DIR'])
    save_dir.mkdir(parents=True, exist_ok=True)
    
    # 创建数据加载器
    train_loader, val_loader, class_names = create_data_loaders(config)
    num_classes = len(class_names)
    
    # 验证类别名称与配置一致
    expected_classes = config['BASE_CLASSES']['NAMES']
    if set(class_names) != set(expected_classes):
        print("⚠️  Warning: Class names don't match config")
        print(f"Found: {sorted(class_names)}")
        print(f"Expected: {sorted(expected_classes)}")
    
    # 创建模型
    model = create_model(config, num_classes, device)
    
    # 创建优化器和调度器
    optimizer, scheduler = create_optimizer_and_scheduler(model, config)
    
    # 损失函数
    criterion = nn.CrossEntropyLoss()
    
    # 训练循环
    print(f"\nStarting training for {config['TRAINING']['EPOCHS']} epochs...")
    best_val_acc = 0.0
    
    for epoch in range(config['TRAINING']['EPOCHS']):
        start_time = time.time()
        
        # 训练
        train_loss, train_acc = train_epoch(
            model, train_loader, criterion, optimizer, device, epoch, config
        )
        
        # 验证
        val_loss, val_acc, val_top5_acc = validate(model, val_loader, criterion, device)
        
        # 学习率调度
        if scheduler:
            scheduler.step()
        
        # 打印结果
        epoch_time = time.time() - start_time
        print(f"\nEpoch {epoch+1}/{config['TRAINING']['EPOCHS']} ({epoch_time:.1f}s)")
        print(f"Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.2f}%")
        print(f"Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.2f}%, Val Top5: {val_top5_acc:.2f}%")
        if scheduler:
            print(f"LR: {optimizer.param_groups[0]['lr']:.6f}")
        
        # 保存最佳模型
        if val_acc > best_val_acc:
            best_val_acc = val_acc
            save_checkpoint(
                model, optimizer, epoch, val_acc, config,
                save_dir / 'best_model.pth'
            )
            print(f"✓ New best validation accuracy: {best_val_acc:.2f}%")
        
        # 定期保存检查点
        checkpoint_period = config['OUTPUT']['CHECKPOINT_PERIOD']
        if (epoch + 1) % checkpoint_period == 0:
            save_checkpoint(
                model, optimizer, epoch, val_acc, config,
                save_dir / f'checkpoint_epoch_{epoch+1}.pth'
            )
        
        print("-" * 60)
    
    print(f"\n🎉 Training completed!")
    print(f"Best validation accuracy: {best_val_acc:.2f}%")
    print(f"Model saved to: {save_dir}")
    
    # 保存最终模型
    save_checkpoint(
        model, optimizer, config['TRAINING']['EPOCHS']-1, best_val_acc, config,
        save_dir / 'final_model.pth'
    )

if __name__ == "__main__":
    main()
