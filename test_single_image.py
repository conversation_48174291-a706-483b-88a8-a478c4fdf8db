#!/usr/bin/env python3
"""
DE-ViT单张图像测试脚本
简化版本，专门用于测试单张图像
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import torch
import numpy as np
import argparse
from pathlib import Path
import cv2
from detectron2.config import get_cfg
import detectron2.data.transforms as T
import detectron2.data.detection_utils as utils
from tools.train_net import Trainer
from PIL import Image, ImageDraw, ImageFont
import matplotlib.pyplot as plt
import seaborn as sns
import random

def setup_config(config_file, model_path, device='cuda'):
    """设置配置"""
    config = get_cfg()
    config.merge_from_file(config_file)
    
    # 基本设置
    config.MODEL.DEVICE = device
    config.MODEL.WEIGHTS = model_path
    config.MODEL.MASK_ON = True
    
    # 冻结配置
    config.freeze()
    return config

def load_model(config, model_path, device='cuda'):
    """加载模型"""
    print(f"Loading model from: {model_path}")
    
    # 构建模型
    model = Trainer.build_model(config).to(device)
    
    # 加载权重
    checkpoint = torch.load(model_path, map_location=device)
    model.load_state_dict(checkpoint['model'])
    model.eval()
    
    print("✓ Model loaded successfully!")
    return model

def preprocess_image(image_path, config):
    """预处理图像"""
    print(f"Processing image: {image_path}")
    
    # 读取图像
    image = utils.read_image(image_path, format="RGB")
    height, width = image.shape[0], image.shape[1]
    
    print(f"Original image size: {width}x{height}")
    
    # 构建数据增强
    augs = utils.build_augmentation(config, False)
    augmentations = T.AugmentationList(augs)
    
    # 应用增强
    aug_input = T.AugInput(image)
    augmentations(aug_input)
    
    # 准备输入数据
    dataset_dict = {
        "height": height,
        "width": width,
        "image": torch.as_tensor(np.ascontiguousarray(aug_input.image.transpose(2, 0, 1)))
    }
    
    return [dataset_dict], image

def run_inference(model, batched_inputs, device='cuda'):
    """运行推理"""
    print("Running inference...")
    
    # 移动数据到设备
    for item in batched_inputs:
        item["image"] = item["image"].to(device)
    
    # 推理
    with torch.no_grad():
        outputs = model(batched_inputs)
    
    print("✓ Inference completed!")
    return outputs[0]

def filter_predictions(instances, threshold=0.5):
    """过滤预测结果"""
    scores = instances.scores
    valid_mask = scores >= threshold
    
    if valid_mask.sum() == 0:
        print(f"No predictions above threshold {threshold}")
        return None, None, None
    
    boxes = instances.pred_boxes.tensor[valid_mask]
    classes = instances.pred_classes[valid_mask]
    scores = scores[valid_mask]
    
    print(f"Found {len(boxes)} predictions above threshold {threshold}")
    return boxes, classes, scores

def assign_colors(pred_classes, label_names, seed=42):
    """为不同类别分配颜色"""
    unique_classes = torch.unique(pred_classes).tolist()
    class_names = list(set([label_names[ci] for ci in unique_classes]))
    
    # 生成颜色
    colors = list(sns.color_palette("hls", len(class_names)).as_hex())
    random.seed(seed)
    random.shuffle(colors)
    
    # 创建类别到颜色的映射
    class2color = {name: color for name, color in zip(class_names, colors)}
    
    # 为每个预测分配颜色
    pred_colors = [class2color[label_names[cid]] for cid in pred_classes.tolist()]
    
    return pred_colors

def visualize_results(image, boxes, classes, scores, label_names, output_path, threshold=0.5):
    """可视化结果"""
    print("Creating visualization...")
    
    # 转换图像格式
    if isinstance(image, np.ndarray):
        pil_image = Image.fromarray(image)
    else:
        pil_image = image
    
    # 创建绘图对象
    draw = ImageDraw.Draw(pil_image)
    
    try:
        font = ImageFont.truetype("arial.ttf", 20)
    except:
        font = ImageFont.load_default()
    
    # 分配颜色
    colors = assign_colors(classes, label_names)
    
    # 绘制边界框和标签
    for box, cls_id, score, color in zip(boxes, classes, scores, colors):
        x1, y1, x2, y2 = box.cpu().numpy()
        
        # 绘制边界框
        draw.rectangle([x1, y1, x2, y2], outline=color, width=3)
        
        # 准备标签文本
        label = f"{label_names[cls_id]}: {score:.2f}"
        
        # 绘制标签背景
        text_bbox = draw.textbbox((x1, y1), label, font=font)
        draw.rectangle(text_bbox, fill=color)
        
        # 绘制标签文本
        draw.text((x1, y1), label, fill="white", font=font)
    
    # 保存结果
    pil_image.save(output_path)
    print(f"✓ Visualization saved to: {output_path}")
    
    return pil_image

def print_results(boxes, classes, scores, label_names):
    """打印检测结果"""
    print("\n" + "="*50)
    print("DETECTION RESULTS")
    print("="*50)
    
    for i, (box, cls_id, score) in enumerate(zip(boxes, classes, scores)):
        x1, y1, x2, y2 = box.cpu().numpy()
        class_name = label_names[cls_id]
        
        print(f"Detection {i+1}:")
        print(f"  Class: {class_name}")
        print(f"  Confidence: {score:.3f}")
        print(f"  Box: [{x1:.1f}, {y1:.1f}, {x2:.1f}, {y2:.1f}]")
        print(f"  Size: {x2-x1:.1f} x {y2-y1:.1f}")
        print()

def main():
    parser = argparse.ArgumentParser(description='Test single image with DE-ViT')
    parser.add_argument('--image', type=str, required=True, help='Path to input image')
    parser.add_argument('--config', type=str, 
                       default='configs/open-vocabulary/coco/vitl.yaml',
                       help='Path to config file')
    parser.add_argument('--model', type=str,
                       help='Path to model weights (auto-detect if not specified)')
    parser.add_argument('--output', type=str, default='output_detection.jpg',
                       help='Path to output image')
    parser.add_argument('--threshold', type=float, default=0.5,
                       help='Detection confidence threshold')
    parser.add_argument('--device', type=str, default='cuda',
                       choices=['cuda', 'cpu'], help='Device to use')
    parser.add_argument('--vit', type=str, default='l',
                       choices=['s', 'b', 'l'], help='ViT model size')
    
    args = parser.parse_args()
    
    # 检查输入文件
    if not os.path.exists(args.image):
        print(f"Error: Image file {args.image} not found!")
        return 1
    
    # 自动检测模型权重
    if args.model is None:
        model_pattern = f"weights/trained/open-vocabulary/coco/vit{args.vit}_*.pth"
        import glob
        model_files = glob.glob(model_pattern)
        if not model_files:
            print(f"Error: No model weights found matching {model_pattern}")
            return 1
        args.model = model_files[0]
        print(f"Auto-detected model: {args.model}")
    
    # 检查设备
    if args.device == 'cuda' and not torch.cuda.is_available():
        print("CUDA not available, using CPU")
        args.device = 'cpu'
    
    try:
        # 1. 设置配置
        config = setup_config(args.config, args.model, args.device)
        
        # 2. 加载模型
        model = load_model(config, args.model, args.device)
        
        # 3. 预处理图像
        batched_inputs, original_image = preprocess_image(args.image, config)
        
        # 4. 运行推理
        output = run_inference(model, batched_inputs, args.device)
        
        # 5. 过滤预测结果
        instances = output['instances']
        boxes, classes, scores = filter_predictions(instances, args.threshold)
        
        if boxes is not None:
            # 6. 打印结果
            print_results(boxes, classes, scores, model.label_names)
            
            # 7. 可视化结果
            visualize_results(original_image, boxes, classes, scores, 
                            model.label_names, args.output, args.threshold)
            
            print(f"\n✓ Detection completed successfully!")
            print(f"Found {len(boxes)} objects")
            print(f"Output saved to: {args.output}")
        else:
            print(f"\nNo objects detected above threshold {args.threshold}")
            print("Try lowering the threshold with --threshold parameter")
        
        return 0
        
    except Exception as e:
        print(f"Error during processing: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
