#!/usr/bin/env python3
"""
DE-ViT Few-Shot目标检测模型传播层可视化
专门针对 task=fsod vit=b dataset=coco shot=10
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import torch
import numpy as np
import cv2
import matplotlib.pyplot as plt
from pathlib import Path
import argparse
from detectron2.config import get_cfg
import detectron2.data.transforms as T
import detectron2.data.detection_utils as utils
from tools.train_net import Trainer

class PropagationFeatureHook:
    """专门捕获传播层Conv2d+BN+ReLU输出的Hook类"""
    
    def __init__(self):
        self.conv_features = None
        self.bn_features = None
        self.relu_features = None
        self.hooks = []
        self.layer_found = False
    
    def register_hooks(self, model):
        """注册hooks到第一个传播层的Conv2d, BatchNorm2d, ReLU"""
        print("Searching for first propagation layer...")
        
        for name, module in model.named_modules():
            if 'per_cls_cnn' in name and hasattr(module, 'main_layers'):
                print(f"Found propagation network: {name}")
                
                # 获取第一个传播层 (Sequential)
                first_layer = module.main_layers[0]
                print(f"First layer structure: {first_layer}")
                
                # 遍历Sequential中的子模块
                conv_found = False
                bn_found = False
                relu_found = False
                
                for i, (sub_name, sub_module) in enumerate(first_layer.named_children()):
                    print(f"  {i}: {sub_name} -> {type(sub_module)}")
                    
                    # Conv2d层
                    if isinstance(sub_module, torch.nn.Conv2d) and not conv_found:
                        hook = sub_module.register_forward_hook(
                            lambda m, inp, out, ln=f"{name}.main_layers[0].{sub_name}": 
                            self._save_conv_feature(m, inp, out, ln)
                        )
                        self.hooks.append(hook)
                        conv_found = True
                        print(f"    ✓ Registered Conv2d hook: {sub_name}")
                    
                    # BatchNorm2d层
                    elif isinstance(sub_module, torch.nn.BatchNorm2d) and not bn_found:
                        hook = sub_module.register_forward_hook(
                            lambda m, inp, out, ln=f"{name}.main_layers[0].{sub_name}": 
                            self._save_bn_feature(m, inp, out, ln)
                        )
                        self.hooks.append(hook)
                        bn_found = True
                        print(f"    ✓ Registered BatchNorm2d hook: {sub_name}")
                    
                    # ReLU层
                    elif isinstance(sub_module, torch.nn.ReLU) and not relu_found:
                        hook = sub_module.register_forward_hook(
                            lambda m, inp, out, ln=f"{name}.main_layers[0].{sub_name}": 
                            self._save_relu_feature(m, inp, out, ln)
                        )
                        self.hooks.append(hook)
                        relu_found = True
                        print(f"    ✓ Registered ReLU hook: {sub_name}")
                
                if conv_found or bn_found or relu_found:
                    self.layer_found = True
                    break
        
        return self.layer_found
    
    def _save_conv_feature(self, module, input, output, layer_name):
        """保存Conv2d输出"""
        self.conv_features = output.detach().cpu()
        print(f"✓ Captured Conv2d output: {output.shape}")
    
    def _save_bn_feature(self, module, input, output, layer_name):
        """保存BatchNorm2d输出"""
        self.bn_features = output.detach().cpu()
        print(f"✓ Captured BatchNorm2d output: {output.shape}")
    
    def _save_relu_feature(self, module, input, output, layer_name):
        """保存ReLU输出"""
        self.relu_features = output.detach().cpu()
        print(f"✓ Captured ReLU output: {output.shape}")
    
    def get_features(self):
        """获取所有捕获的特征"""
        features = {}
        if self.conv_features is not None:
            features['Conv2d'] = self.conv_features
        if self.bn_features is not None:
            features['BatchNorm2d'] = self.bn_features
        if self.relu_features is not None:
            features['ReLU'] = self.relu_features
        return features
    
    def cleanup(self):
        """清理hooks"""
        for hook in self.hooks:
            hook.remove()
        self.hooks.clear()
        print("✓ Hooks cleaned up")

def setup_fsod_config(model_weights, rpn_config, device='cuda'):
    """设置Few-Shot目标检测配置"""
    config = get_cfg()
    
    # 使用ViT-Base 10-shot配置
    config.merge_from_file('configs/few-shot/vitb_shot10.yaml')
    
    # 设置权重路径
    config.MODEL.WEIGHTS = model_weights
    config.DE.OFFLINE_RPN_CONFIG = rpn_config
    
    # 基本设置
    config.MODEL.DEVICE = device
    config.MODEL.MASK_ON = True
    config.MODEL.ROI_HEADS.NUM_CLASSES = 80  # COCO类别数
    
    print(f"✓ Model weights: {model_weights}")
    print(f"✓ RPN config: {rpn_config}")
    
    config.freeze()
    return config

def load_fsod_model(config, device='cuda'):
    """加载Few-Shot目标检测模型"""
    print("Loading FSOD model...")
    
    model = Trainer.build_model(config).to(device)
    
    # 加载权重
    checkpoint = torch.load(config.MODEL.WEIGHTS, map_location=device)
    if 'model' in checkpoint:
        state_dict = checkpoint['model']
    else:
        state_dict = checkpoint
    
    # 灵活加载权重
    try:
        model.load_state_dict(state_dict, strict=True)
        print("✓ Model weights loaded (strict)")
    except RuntimeError as e:
        print(f"⚠️  Strict loading failed, using flexible loading...")
        model.load_state_dict(state_dict, strict=False)
        print("✓ Model weights loaded (flexible)")
    
    model.eval()
    print("✓ FSOD model ready!")
    return model

def preprocess_image(image_path, config):
    """预处理图像"""
    print(f"Processing image: {image_path}")
    
    if not os.path.exists(image_path):
        raise FileNotFoundError(f"Image not found: {image_path}")
    
    # 读取图像
    image = utils.read_image(image_path, format="RGB")
    height, width = image.shape[0], image.shape[1]
    print(f"Original image size: {width}x{height}")
    
    # 数据增强
    augs = utils.build_augmentation(config, False)
    augmentations = T.AugmentationList(augs)
    
    aug_input = T.AugInput(image)
    augmentations(aug_input)
    
    dataset_dict = {
        "height": height,
        "width": width,
        "image": torch.as_tensor(np.ascontiguousarray(aug_input.image.transpose(2, 0, 1)))
    }
    
    return [dataset_dict], image

def visualize_propagation_features(features_dict, output_dir, max_channels=16):
    """可视化传播层特征"""
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    for stage_name, features in features_dict.items():
        print(f"Visualizing {stage_name}: {features.shape}")
        
        if len(features.shape) != 4:
            print(f"Skipping {stage_name}: unexpected shape")
            continue
        
        batch_size, num_channels, height, width = features.shape
        features = features[0]  # 第一个batch
        num_channels = min(num_channels, max_channels)
        
        # 创建网格可视化
        cols = 4
        rows = (num_channels + cols - 1) // cols
        
        fig, axes = plt.subplots(rows, cols, figsize=(cols*3, rows*3))
        if rows == 1:
            axes = axes.reshape(1, -1)
        
        for i in range(num_channels):
            row = i // cols
            col = i % cols
            
            feature_map = features[i].numpy()
            
            # 归一化
            if feature_map.max() > feature_map.min():
                feature_map = (feature_map - feature_map.min()) / (feature_map.max() - feature_map.min())
            
            im = axes[row, col].imshow(feature_map, cmap='viridis')
            axes[row, col].set_title(f'Channel {i}')
            axes[row, col].axis('off')
            plt.colorbar(im, ax=axes[row, col], fraction=0.046, pad=0.04)
        
        # 隐藏多余子图
        for i in range(num_channels, rows * cols):
            row = i // cols
            col = i % cols
            axes[row, col].axis('off')
        
        plt.suptitle(f'First Propagation Layer - {stage_name}\n'
                    f'Conv2d → BatchNorm2d → ReLU', fontsize=14)
        plt.tight_layout()
        
        # 保存
        output_file = output_path / f"propagation_{stage_name.lower()}_features.png"
        plt.savefig(output_file, dpi=150, bbox_inches='tight')
        plt.close()
        
        print(f"✓ Saved: {output_file}")
        
        # 保存单独通道
        channel_dir = output_path / f"propagation_{stage_name.lower()}_channels"
        channel_dir.mkdir(exist_ok=True)
        
        for i in range(num_channels):
            feature_map = features[i].numpy()
            
            if feature_map.max() > feature_map.min():
                feature_map = (feature_map - feature_map.min()) / (feature_map.max() - feature_map.min())
            feature_map = (feature_map * 255).astype(np.uint8)
            
            channel_file = channel_dir / f"channel_{i:03d}.png"
            cv2.imwrite(str(channel_file), feature_map)
        
        print(f"✓ Saved channels: {channel_dir}")

def main():
    parser = argparse.ArgumentParser(
        description='Visualize DE-ViT FSOD first propagation layer features',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Example usage:
  python visualize_fsod_propagation.py \\
    --image path/to/test_image.jpg \\
    --model-weights path/to/vitb_shot10_model.pth \\
    --rpn-config path/to/rpn_config.yaml \\
    --output propagation_visualization
        """
    )
    
    parser.add_argument('--image', type=str, required=True,
                       help='Path to test image')
    parser.add_argument('--model-weights', type=str, required=True,
                       help='Path to trained FSOD model weights (MODEL.WEIGHTS)')
    parser.add_argument('--rpn-config', type=str, required=True,
                       help='Path to RPN config file (DE.OFFLINE_RPN_CONFIG)')
    parser.add_argument('--output', type=str, default='fsod_propagation_features',
                       help='Output directory for visualizations')
    parser.add_argument('--device', type=str, default='cuda',
                       choices=['cuda', 'cpu'], help='Device to use')
    parser.add_argument('--max-channels', type=int, default=16,
                       help='Maximum channels to visualize')
    
    args = parser.parse_args()
    
    # 检查文件存在性
    if not os.path.exists(args.image):
        print(f"❌ Image not found: {args.image}")
        return 1
    
    if not os.path.exists(args.model_weights):
        print(f"❌ Model weights not found: {args.model_weights}")
        return 1
    
    if not os.path.exists(args.rpn_config):
        print(f"❌ RPN config not found: {args.rpn_config}")
        return 1
    
    # 检查设备
    if args.device == 'cuda' and not torch.cuda.is_available():
        print("CUDA not available, using CPU")
        args.device = 'cpu'
    
    # 创建特征提取器
    feature_hook = PropagationFeatureHook()
    
    try:
        print("DE-ViT FSOD Propagation Layer Visualization")
        print("=" * 50)
        print(f"Task: Few-Shot Object Detection")
        print(f"Model: ViT-Base, 10-shot, COCO")
        print(f"Target: First propagation layer Conv2d→BN→ReLU")
        print()
        
        # 1. 设置配置
        config = setup_fsod_config(args.model_weights, args.rpn_config, args.device)
        
        # 2. 加载模型
        model = load_fsod_model(config, args.device)
        
        # 3. 注册hooks
        if not feature_hook.register_hooks(model):
            print("❌ Failed to find propagation layers!")
            return 1
        
        # 4. 预处理图像
        batched_inputs, original_image = preprocess_image(args.image, config)
        
        # 5. 运行推理
        print("\nRunning inference with feature capture...")
        for item in batched_inputs:
            item["image"] = item["image"].to(args.device)
        
        with torch.no_grad():
            outputs = model(batched_inputs)
        
        print("✓ Inference completed")
        
        # 6. 获取特征
        captured_features = feature_hook.get_features()
        
        if captured_features:
            print(f"\n✓ Captured features from {len(captured_features)} stage(s):")
            for stage, features in captured_features.items():
                print(f"  {stage}: {features.shape}")
            
            # 7. 可视化
            visualize_propagation_features(captured_features, args.output, args.max_channels)
            
            print(f"\n🎉 Propagation layer visualization completed!")
            print(f"Output directory: {args.output}")
            print(f"\nVisualized stages:")
            for stage in captured_features.keys():
                print(f"  - {stage} (Conv2d→BatchNorm2d→ReLU pipeline)")
        else:
            print("\n❌ No features captured!")
            print("Please check if the model contains propagation layers.")
        
        return 0
        
    except Exception as e:
        print(f"\n💥 Error: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    finally:
        feature_hook.cleanup()

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
