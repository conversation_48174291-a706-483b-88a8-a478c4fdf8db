# output dir
/output
instant_test_output
inference_test_output
oai_clip_weights

pt_output
pretrained_ckpt
datasets/coco
datasets/lvis
datasets/cifar-10-batches-py

*.png
*.json
!vscode_launch.json
!/detectron2/data/classnames/*.json
*.diff
!/projects/DensePose/doc/images/*.jpg

# compilation and distribution
__pycache__
_ext
*.pyc
*.pyd
*.so
*.dll
*.egg-info/
build/
dist/
wheels/

# pytorch/python/numpy formats
*.pth
*.pkl
*.npy
*.ts
model_ts*.txt

# ipython/jupyter notebooks
# *.ipynb
**/.ipynb_checkpoints/

# Editor temporaries
*.swn
*.swo
*.swp
*~

# editor settings
.idea
.vscode
_darcs

# project dirs
/detectron2/model_zoo/configs
/datasets/*
!/datasets/*.*
!/datasets/custom_images/
!/datasets/custom_images/*.jpg
!/docs
!/docs/*.*
/projects/*/datasets
/models
/snippet

tmp.*
tensorboard

/weights
/datasets
!demo/ycb_prototypes.pth