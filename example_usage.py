#!/usr/bin/env python3
"""
使用示例：可视化DINOv2 ViT-Base backbone特征
"""

import os
import sys
import subprocess

def run_feature_visualization():
    """
    运行特征可视化的示例
    """
    
    # 示例图像路径 (请替换为您的图像路径)
    image_path = "path/to/your/image.jpg"
    
    # 输出目录
    output_dir = "feature_visualizations"
    
    # 检查图像是否存在
    if not os.path.exists(image_path):
        print("请修改 image_path 为您的图像路径")
        print("例如: image_path = 'demo/sample_image.jpg'")
        return
    
    # 构建命令
    cmd = [
        "python", "visualize_backbone_features.py",
        "--image", image_path,
        "--output", output_dir,
        "--device", "cuda",  # 如果没有GPU，改为 "cpu"
        "--target_size", "800",
        "--max_size", "1333"
    ]
    
    print("Running feature visualization...")
    print("Command:", " ".join(cmd))
    
    try:
        # 运行命令
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("Success!")
        print(result.stdout)
        
    except subprocess.CalledProcessError as e:
        print("Error occurred:")
        print(e.stderr)
        print(e.stdout)


def create_batch_script():
    """
    创建批处理脚本，用于处理多张图像
    """
    
    batch_script = """#!/usr/bin/env python3
import os
import glob
from pathlib import Path

# 图像目录
image_dir = "path/to/your/images"  # 修改为您的图像目录
output_base_dir = "batch_feature_visualizations"

# 支持的图像格式
image_extensions = ["*.jpg", "*.jpeg", "*.png", "*.bmp", "*.tiff"]

# 获取所有图像文件
image_files = []
for ext in image_extensions:
    image_files.extend(glob.glob(os.path.join(image_dir, ext)))
    image_files.extend(glob.glob(os.path.join(image_dir, ext.upper())))

print(f"Found {len(image_files)} images")

# 处理每张图像
for i, image_path in enumerate(image_files):
    print(f"\\nProcessing {i+1}/{len(image_files)}: {image_path}")
    
    image_name = Path(image_path).stem
    output_dir = os.path.join(output_base_dir, image_name)
    
    cmd = [
        "python", "visualize_backbone_features.py",
        "--image", image_path,
        "--output", output_dir,
        "--device", "cuda",
        "--target_size", "800",
        "--max_size", "1333"
    ]
    
    try:
        import subprocess
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print(f"✓ Success: {image_name}")
    except subprocess.CalledProcessError as e:
        print(f"✗ Failed: {image_name}")
        print(f"Error: {e.stderr}")

print("\\nBatch processing complete!")
"""
    
    with open("batch_visualize.py", "w") as f:
        f.write(batch_script)
    
    print("Created batch_visualize.py for processing multiple images")


if __name__ == "__main__":
    print("=== DINOv2 ViT-Base Feature Visualization ===")
    print()
    print("1. Single image example:")
    print("   Modify image_path in this script and run:")
    print("   python example_usage.py")
    print()
    print("2. Direct command line usage:")
    print("   python visualize_backbone_features.py --image your_image.jpg --output output_dir")
    print()
    print("3. Batch processing:")
    print("   Run create_batch_script() to generate batch processing script")
    print()
    
    # 取消注释下面的行来运行示例
    # run_feature_visualization()
    
    # 取消注释下面的行来创建批处理脚本
    # create_batch_script()
